// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

import { Fees } from "src/types/Fees.sol";
import { Presale, Membership } from "src/types/Configuration.sol";

import { VestController } from "src/VestController.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { VestMembershipDescriptor } from "src/VestMembershipDescriptor.sol";
import { VestPresaleDeployer } from "src/VestPresaleDeployer.sol";
import { VestMembershipDeployer } from "src/VestMembershipDeployer.sol";

import { Test } from "./Test.sol";
import { ERC20Sample } from "./samples/ERC20Sample.sol";

contract VestControllerTest is Test {
    uint8 internal decimalsA = uint8(vm.envOr("TEST_TOKEN_A_DECIMALS", uint8(18)));
    uint8 internal decimalsB = uint8(vm.envOr("TEST_TOKEN_B_DECIMALS", uint8(6)));

    VestController internal controller;

    IERC20 internal tokenA;
    IERC20 internal tokenB;

    VestPresaleDeployer presaleDeployer = new VestPresaleDeployer();
    VestMembershipDeployer membershipDeployer = new VestMembershipDeployer();
    VestMembershipDescriptor membershipDescriptor = new VestMembershipDescriptor();

    function setUp() public virtual {
        tokenA = new ERC20Sample(decimalsA, 0);
        tokenB = new ERC20Sample(decimalsB, 0);

        controller = new VestController(presaleDeployer, membershipDeployer, composeFees());
    }

    function composeFees() internal pure returns (Fees memory) {
        return Fees({
            tokenANumerator: 1,
            tokenADenominator: 10,
            tokenBNumerator: 20,
            tokenBDenominator: 100,
            nftNumerator: 500,
            nftDenominator: 10_000
        });
    }

    function composeConfiguration() internal returns (Presale.Configuration memory, Membership.Configuration memory) {
        Fees memory fees = composeFees();

        Presale.Configuration memory presale = Presale.Configuration({
            fees: Presale.Fees({
                tokenANumerator: fees.tokenANumerator,
                tokenADenominator: fees.tokenADenominator,
                tokenBNumerator: fees.tokenBNumerator,
                tokenBDenominator: fees.tokenBDenominator
            }),
            tokenA: tokenA,
            tokenB: tokenB,
            manager: manager,
            beneficiary: beneficiary,
            tgeTimestamp: vm.unixTime(),
            listingTimestamp: vm.unixTime(),
            claimbackPeriod: 7 days
        });

        Membership.Configuration memory membership = Membership.Configuration({
            fees: Membership.Fees({ numerator: fees.nftNumerator, denominator: fees.nftDenominator }),
            descriptor: membershipDescriptor,
            metadata: IVestMembership.Metadata({ token: address(tokenA), description: "", color: "" })
        });

        return (presale, membership);
    }
}
