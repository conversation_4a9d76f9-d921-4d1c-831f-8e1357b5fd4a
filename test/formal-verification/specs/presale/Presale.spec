import "../methods/IPresale.spec";

using Membership as membership;
using ERC20Sample as tokenA;
using ERC20Sample as tokenB;

/***
 * # Presale
 */

//// ## Part 1: Basic rules ////////////////////////////////////////////////////
/* 
	Property: Find and show a path for each method.
*/

// rule reachability(method f) {
// 	env e;
// 	calldataarg args;
// 	f(e,args);
// 	satisfy true, "a non-reverting path through this method was found";
// }

function setUp(env e) {
	// check that memberships and tokens used in tests is the same as tested protocol use.
	require membership == membership(e);
	
	require tokenA == tokenA(e);
	require tokenB == tokenB(e);
}

function senderNotProtocolContract(env e) {
	require e.msg.sender != currentContract;
	
	require membership != e.msg.sender;
	
	require tokenA != e.msg.sender;
	require tokenB != e.msg.sender;
}

function senderIsOwnerOfSaleMembership(env e, uint256 membershipId) {
	require membership.ownerOf(e, membershipId) == e.msg.sender;
}

function senderIsOwnerOfVestingMembership(env e, uint256 membershipId) {
	require membership.ownerOf(e, membershipId) == e.msg.sender;
}

rule UsageShouldReduceAfterClaimback(env e, uint256 membershipId, uint256 amount) { // #wip
	setUp(e);

	//get usage before claimback
	IMembership.Usage usage_before = membership.getUsage(e, membershipId);
	mathint current_usage_before = usage_before.current;

	require to_mathint(liquidityA()) >= current_usage_before;

	IMembership.Attributes attributes = membership.getAttributes(e, membershipId);

	require attributes.price > 0;

	require current_usage_before > 0;
	require current_usage_before <= to_mathint(usage_before.max);
	require to_mathint(amount) <= current_usage_before;
	//amount to claimback higher than 0
	require amount > 0;

	mathint balance_before = tokenB.balanceOf(e, e.msg.sender);

	require balance_before <= 100;

	mathint contract_balance = tokenB.balanceOf(e, currentContract);

	require balance_before + contract_balance <= max_uint256;

	claimback(e, membershipId, amount);

	mathint balance_after = tokenB.balanceOf(e, e.msg.sender);


	// user should get a tokens B
	assert balance_after > balance_before;

	// get usage after claimback
	IMembership.Usage usage_after = membership.getUsage(e, membershipId);
	mathint current_usage_after = usage_after.current;

	// usage should be reduced
	assert current_usage_before > current_usage_after;  // TODO it is sometimes not reduced.
}

rule UpdateWhitelist(env e, bytes32 root, string proofsURI, uint256 roundId) {
	Presale.Round round_before = getRound(e, roundId);
	bytes32 whitelist_before = round_before.whitelistRoot;

	updateWhitelist(e, roundId, root, proofsURI);

	Presale.Round round_after = getRound(e, roundId);
	bytes32 whitelist_after = round_after.whitelistRoot;

	// whitelist updated only by manager
	assert whitelist_before != whitelist_after => e.msg.sender == manager(e); 
}

rule UpdateListingTimestamp(env e, uint256 listingTimestamp_) {
	require listingTimestamp_ > 0;
	require listingTimestamp_ > e.block.timestamp;
	require listingTimestamp() != 0 && e.block.timestamp <= getTgeTimestamp(e);

	updateListingTimestamp(e, listingTimestamp_);

	// listing timestamp updated only by manager
	assert listingTimestamp() == listingTimestamp_ => e.msg.sender == manager();
}

rule DepositTokenA(env e, uint256 amount) {	
	// check liquidity A pool balance before deposit.
	uint256 liquidity_before = liquidityA();

	mathint tokenA_balance_before = tokenA.balanceOf(e, currentContract);

	// overflow check
	require amount + liquidity_before <= max_uint256;

	// can be done by anyone
	depositTokenA(e, amount);

	mathint tokenA_balance_after = tokenA.balanceOf(e, currentContract);

	uint256 liquidity_after = liquidityA();

	// token A balance of contract is increased by deposited amount
	assert tokenA_balance_before + amount == to_mathint(tokenA_balance_after);

	// liquidity A balance updated
	assert liquidity_before + amount == to_mathint(liquidity_after);
}

rule NotPossibleClaimAfterClaimbackCurrentUsage(env e, uint256 membershipId, uint256 amount) {
	IMembership.Usage usage_before = membership.getUsage(e, membershipId);

	claimback(e, membershipId, amount);

	IMembership.Usage usage_after = membership.getUsage(e, membershipId);

	require usage_after.current == 0;

	uint256 balance_before = tokenA.balanceOf(e, e.msg.sender);

	senderIsOwnerOfSaleMembership(e, membershipId);
	claimWithSaleMembership@withrevert(e, membershipId);

	// transaction will be reverted
	assert lastReverted;

	uint256 balance_after = tokenA.balanceOf(e, e.msg.sender);

    // token A balance of user will be not changed
	assert balance_before == balance_after;
}

// rule UserMakeClaimWithSaleMembership(env e, uint256 membershipId) {
// 	uint256 balance_before = tokenA.balanceOf(e, e.msg.sender);
// 	uint256 unlocked_amount = membership.unlocked(e, membershipId);
// 	require unlocked_amount > 0;

// 	IMembership.Usage usage_before = membership.getUsage(membershipId);
// 	mathint usage_before_current = to_mathint(usage_before.current);
// 	require to_mathint(usage_before.max) >= usage_before_current;
// 	require usage_before_current > 0;

// 	// user can make claim
// 	claimWithSaleMembership(e, membershipId);
	
// 	// usage of current sale membership will be reduced to zero
//     IMembership.Usage usage_after = membership.getUsage(membershipId);
// 	uint256 usage_after_current = usage_after.current;
// 	assert usage_after_current == 0;

// 	mathint balance_after = tokenA.balanceOf(e, e.msg.sender);

// 	// token A balance of user will be increased by claimed amount
// 	mathint first_argument = balance_before + unlocked_amount;
// 	assert first_argument == balance_after;
// }

rule UserMakeClaimWithVestingMembership(env e) {
	setUp(e);
	senderNotProtocolContract(e);

	uint256 membershipId;

	require e.msg.sender != currentContract;
	uint256 balance_before = tokenA.balanceOf(e, e.msg.sender);
	mathint unlocked_amount = membership.unlocked(e, membershipId);
	require unlocked_amount > 0;
	require unlocked_amount < 100; // TODO remove

	// overflow protection
	require balance_before + unlocked_amount <= max_uint256;


	require unlocked_amount > 5;

	IMembership.Usage usage_before = membership.getUsage(e, membershipId);
	mathint usage_before_current = to_mathint(usage_before.current);

	require to_mathint(usage_before.max) > usage_before_current;

	require e.msg.sender == membership.ownerOf(e, membershipId);

	require unlocked_amount > usage_before_current;

	// user can make claim
	claimWithVestingMembership(e, membershipId);
	
	// usage of current vesting membership will be reduced to zero
    IMembership.Usage usage_after = membership.getUsage(e, membershipId);
	mathint usage_after_current = usage_after.current;

	mathint balance_after = tokenA.balanceOf(e, e.msg.sender);

	assert usage_after_current == unlocked_amount;


	// token A balance of user will be increased by claimed amount
	mathint expected_balance = balance_before + unlocked_amount - usage_before_current;
	assert expected_balance == balance_after;
}

//// ## Part 2: Invariants ////////////////////////////////////////////////////

// invariant totalSupplyCannotBeHigherThanMaxUint256(env e)
// 	tokenA.totalSupply(e) <= max_uint256;

// 122978293824730344295742555136306447488230798480700562949953421312
// 12297829382473034429574255513630644748823079848070056294995342131263