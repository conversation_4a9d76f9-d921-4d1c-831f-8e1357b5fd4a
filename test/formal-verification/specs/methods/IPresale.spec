methods {
    
    function addRound(Presale.Round round) external optional;
    function beneficiary() external returns (address) envfree;
    function buy(uint256, uint256, IMembership.Attributes, bytes32[]) external returns (uint256) optional; 
    function buyWithSaleMembership(uint256, uint256) external returns (uint256) optional;
    function claimWithSaleMembership(uint256) external returns (uint256, uint256) optional;
    function claimWithVestingMembership(uint256) external returns (uint256) optional;
    function depositTokenA(uint256) external optional;
    function getFeeCollector() external returns (address) envfree;
    function getFees() external returns (Presale.Fees memory) envfree;
    function getRound(uint256 roundId) external returns (Presale.Round memory) envfree;
    function getRoundState(uint256 roundId) external returns (Presale.RoundState);
    function getRounds() external returns (uint256[] memory, Presale.Round[] memory, Presale.RoundState[] memory);
    function getTgeTimestamp() external returns (uint256);
    function liquidityA() external returns (uint256) envfree;
    function liquidityB() external returns (uint256) envfree;
    function listingTimestamp() external returns (uint256) envfree;
    function manager() external returns (address) envfree;
    function nonClaimableBackTokenB() external returns (uint256) envfree;
    function parentVest() external returns (address) envfree;
    function claimback(uint256 membershipId, uint256 amountA) external returns (uint256) optional;
    function claimbackPeriod() external returns (uint256) envfree;
    function removeRound(uint256) external optional;
    function roundParticipants(uint256, address) external returns (bool) envfree;
    function tokenA() external returns (address);
    function tokenB() external returns (address);
    function updateListingTimestamp(uint256) external optional;
    function updateRound(uint256, Presale.Round) external optional;
    function updateWhitelist(uint256, bytes32, string) external optional;
    function membership() external returns (address);
    function withdrawCoin(address) external optional;
    function withdrawToken(address, address, uint256) external optional;
    function withdrawTokenA(uint256) external optional;
    function withdrawTokenB() external optional;
}
