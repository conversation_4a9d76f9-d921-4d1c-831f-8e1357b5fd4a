// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { <PERSON><PERSON><PERSON> } from "murky/Merkle.sol";

library Participants {
    struct Participant {
        address account;
        uint256 price;
        uint256 allocation;
        uint256 claimbackPeriod;
        uint32 tgeNumerator;
        uint32 tgeDenominator;
        uint32 cliffDuration;
        uint32 cliffNumerator;
        uint32 cliffDenominator;
        uint32 vestingPeriodCount;
        uint32 vestingPeriodDuration;
        uint8 tradeable;
        bytes32[] proof;
    }

    struct Collection {
        mapping(uint256 roundId => address[]) accounts;
        mapping(uint256 roundId => mapping(address => Participant[])) items;
    }

    function add(
        Collection storage collection,
        uint256 roundId,
        address account,
        uint256 price,
        uint256 allocation,
        uint256 claimbackPeriod,
        uint32 tgeNumerator,
        uint32 tgeDenominator,
        uint32 cliffDuration,
        uint32 cliffNumerator,
        uint32 cliffDenominator,
        uint32 vestingPeriodCount,
        uint32 vestingPeriodDuration
    ) internal {
        add(
            collection,
            roundId,
            account,
            price,
            allocation,
            claimbackPeriod,
            tgeNumerator,
            tgeDenominator,
            cliffDuration,
            cliffNumerator,
            cliffDenominator,
            vestingPeriodCount,
            vestingPeriodDuration,
            2
        );
    }

    function add(
        Collection storage collection,
        uint256 roundId,
        address account,
        uint256 price,
        uint256 allocation,
        uint256 claimbackPeriod,
        uint32 tgeNumerator,
        uint32 tgeDenominator,
        uint32 cliffDuration,
        uint32 cliffNumerator,
        uint32 cliffDenominator,
        uint32 vestingPeriodCount,
        uint32 vestingPeriodDuration,
        uint8 tradeable
    ) internal {
        if (!exists(collection, roundId, account)) collection.accounts[roundId].push(account);

        Participant memory participant = Participant({
            account: account,
            price: price,
            allocation: allocation,
            claimbackPeriod: claimbackPeriod,
            tgeNumerator: tgeNumerator,
            tgeDenominator: tgeDenominator,
            cliffDuration: cliffDuration,
            cliffNumerator: cliffNumerator,
            cliffDenominator: cliffDenominator,
            vestingPeriodCount: vestingPeriodCount,
            vestingPeriodDuration: vestingPeriodDuration,
            tradeable: tradeable,
            proof: new bytes32[](0)
        });

        collection.items[roundId][account].push(participant);
    }

    function replace(
        Collection storage collection,
        uint256 roundId,
        address account,
        uint256 price,
        uint256 allocation,
        uint256 claimbackPeriod,
        uint32 tgeNumerator,
        uint32 tgeDenominator,
        uint32 cliffDuration,
        uint32 cliffNumerator,
        uint32 cliffDenominator,
        uint32 vestingPeriodCount,
        uint32 vestingPeriodDuration
    ) internal {
        replace(
            collection,
            roundId,
            account,
            price,
            allocation,
            claimbackPeriod,
            tgeNumerator,
            tgeDenominator,
            cliffDuration,
            cliffNumerator,
            cliffDenominator,
            vestingPeriodCount,
            vestingPeriodDuration,
            2
        );
    }

    function replace(
        Collection storage collection,
        uint256 roundId,
        address account,
        uint256 price,
        uint256 allocation,
        uint256 claimbackPeriod,
        uint32 tgeNumerator,
        uint32 tgeDenominator,
        uint32 cliffDuration,
        uint32 cliffNumerator,
        uint32 cliffDenominator,
        uint32 vestingPeriodCount,
        uint32 vestingPeriodDuration,
        uint8 tradeable
    ) internal {
        Participant memory participant = Participant({
            account: account,
            price: price,
            allocation: allocation,
            claimbackPeriod: claimbackPeriod,
            tgeNumerator: tgeNumerator,
            tgeDenominator: tgeDenominator,
            cliffDuration: cliffDuration,
            cliffNumerator: cliffNumerator,
            cliffDenominator: cliffDenominator,
            vestingPeriodCount: vestingPeriodCount,
            vestingPeriodDuration: vestingPeriodDuration,
            tradeable: tradeable,
            proof: new bytes32[](0)
        });

        replace(collection, roundId, participant.account, 0, participant);
    }

    function replace(
        Collection storage collection,
        uint256 roundId,
        address account,
        uint256 index,
        Participant memory participant
    ) internal {
        collection.items[roundId][account][index] = participant;
    }

    function get(Collection storage collection, uint256 roundId, address account)
        internal
        view
        returns (Participant memory)
    {
        return get(collection, roundId, account, 0);
    }

    function get(Collection storage collection, uint256 roundId, address account, uint256 index)
        internal
        view
        returns (Participant memory)
    {
        return collection.items[roundId][account][index];
    }

    function count(Collection storage collection, uint256 roundId) internal view returns (uint256) {
        return collection.accounts[roundId].length;
    }

    function exists(Collection storage collection, uint256 roundId, address account) internal view returns (bool) {
        address[] memory addresses = collection.accounts[roundId];

        for (uint256 i = 0; i < addresses.length; i++) {
            if (addresses[i] != account) continue;

            return true;
        }

        return false;
    }

    function getWhitelistRoot(Collection storage collection, uint256 roundId) internal returns (bytes32) {
        Merkle merkle = new Merkle();

        address[] memory accounts = collection.accounts[roundId];

        uint256 size = 0;
        for (uint256 i = 0; i < accounts.length; i++) {
            address account = accounts[i];

            size += collection.items[roundId][account].length + 1;
        }

        if (size <= 1) return bytes32(0);

        bytes32[] memory data = new bytes32[](size);

        uint256 pointer0;
        for (uint256 i = 0; i < accounts.length; i++) {
            address account = accounts[i];

            Participant[] memory attributes = collection.items[roundId][account];

            for (uint256 j = 0; j < attributes.length; j++) {
                Participant memory participant = attributes[j];

                data[pointer0] = keccak256(
                    abi.encode(
                        account,
                        participant.price,
                        participant.allocation,
                        participant.claimbackPeriod,
                        participant.tgeNumerator,
                        participant.tgeDenominator,
                        participant.cliffDuration,
                        participant.cliffNumerator,
                        participant.cliffDenominator,
                        participant.vestingPeriodCount,
                        participant.vestingPeriodDuration,
                        participant.tradeable
                    )
                );

                pointer0++;
            }
        }

        bytes32 root = merkle.getRoot(data);

        uint256 pointer1;
        for (uint256 i = 0; i < accounts.length; i++) {
            address account = accounts[i];

            Participant[] memory attributes = collection.items[roundId][account];

            for (uint256 j = 0; j < attributes.length; j++) {
                collection.items[roundId][account][j].proof = merkle.getProof(data, pointer1);

                pointer1++;
            }
        }

        return root;
    }

    function clear(Collection storage collection, uint256 roundId) internal {
        address[] memory accounts = collection.accounts[roundId];
        for (uint256 i = 0; i < accounts.length; i++) {
            address account = accounts[i];

            for (uint256 j = 0; j < collection.items[roundId][account].length; j++) {
                collection.items[roundId][account].pop();
            }
        }

        collection.accounts[roundId] = new address[](0);
    }
}
