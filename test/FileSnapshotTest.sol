// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Test } from "forge-std/Test.sol";
import { EnumerableSet } from "@openzeppelin/contracts/utils/structs/EnumerableSet.sol";
import { Fs } from "test/utils/Fs.sol";

/**
 * Solidity implementation of testing using file snapshots inspired by [Jest](https://jestjs.io/docs/snapshot-testing).
 *
 * This saves the result to a file.
 *
 * During subsequent test runs, it reads the file and compares with the result.
 *
 * Useful for testing on-chain svgs, json files, etc.
 *
 * By default, it saves all files inside 'file-snapshots' directory but that can be customized with FILE_SNAPSHOT_DIR environment variable.
 *
 * For this contract to work, make sure you give it required file permissions in your foundry.toml:
 * fs_permissions = [{ access = "read-write", path = "file-snapshots" }]
 */
contract FileSnapshotTest is Test {
    using EnumerableSet for EnumerableSet.Bytes32Set;

    struct FileSnapshot {
        string path;
        string contents;
    }

    // https://github.com/OpenZeppelin/openzeppelin-contracts/issues/3256#issuecomment-1075670855
    uint256 setIndex = 1;
    mapping(uint256 setIndex => EnumerableSet.Bytes32Set) private snapshotPathSets;
    mapping(bytes32 pathHash => FileSnapshot) private snapshots;

    /**
     * Use this modifier to compare the result with the file contents.
     *
     * If you want to update the file, simply remove it before running the test.
     */
    modifier fileSnapshotTest() {
        _beforeFileSnapshotTest();
        _;
        EnumerableSet.Bytes32Set storage snapshotPathSet = snapshotPathSets[setIndex];
        for (uint256 i = 0; i < snapshotPathSet.length(); i++) {
            bytes32 pathHash = snapshotPathSet.at(i);
            FileSnapshot memory fileSnapshot = snapshots[pathHash];
            fileSnapshot.path = _getFullPath(fileSnapshot.path);
            string memory dirname = Fs.dirname(fileSnapshot.path);
            vm.createDir(dirname, true);
            if (vm.exists(fileSnapshot.path)) {
                string memory expected = vm.readFile(fileSnapshot.path);
                assertEq(
                    expected,
                    fileSnapshot.contents,
                    string.concat("File contents at path ", fileSnapshot.path, " does not match the test result")
                );
            } else {
                vm.writeFile(fileSnapshot.path, fileSnapshot.contents);
            }
        }
    }

    /**
     * Use this modifier with watch mode during development to continiously write result to a file.
     * Never commit tests with this modifier.
     */
    modifier fileSnapshotDev() {
        _beforeFileSnapshotTest();
        _;
        EnumerableSet.Bytes32Set storage snapshotPathSet = snapshotPathSets[setIndex];
        for (uint256 i = 0; i < snapshotPathSet.length(); i++) {
            bytes32 pathHash = snapshotPathSet.at(i);
            FileSnapshot memory fileSnapshot = snapshots[pathHash];
            fileSnapshot.path = _getFullPath(fileSnapshot.path);
            string memory dirname = Fs.dirname(fileSnapshot.path);
            vm.createDir(dirname, true);
            vm.writeFile(fileSnapshot.path, fileSnapshot.contents);
        }
    }

    function addFileSnapshot(string memory path, string memory contents) internal {
        bytes32 pathHash = keccak256(abi.encodePacked(path));
        snapshotPathSets[setIndex].add(pathHash);
        snapshots[pathHash] = FileSnapshot({ path: path, contents: contents });
    }

    function _beforeFileSnapshotTest() private {
        setIndex++;
    }

    function _getFullPath(string memory path) private returns (string memory) {
        return string.concat(vm.envOr("FILE_SNAPSHOT_DIR", string("file-snapshots")), "/", path);
    }
}
