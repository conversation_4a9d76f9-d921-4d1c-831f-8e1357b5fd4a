// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Fees } from "src/types/Fees.sol";
import { Round } from "src/types/Round.sol";
import { Errors } from "src/libraries/Errors.sol";
import { Presale, Membership } from "src/types/Configuration.sol";
import { VestPresale } from "src/VestPresale.sol";
import { VestController } from "src/VestController.sol";

import { VestControllerTest } from "test/VestControllerTest.sol";

contract Vest_createPresale is VestControllerTest {
    Fees internal fees = composeFees();

    modifier whenAnOwnerRequestsCustomFees() {
        _;
    }

    function test_WhenCustomFeesAreValid() external whenAnOwnerRequestsCustomFees {
        fees.tokenANumerator = fees.tokenANumerator - 1;
        assertTrue(controller.getFees().tokenANumerator != fees.tokenANumerator, "fees differ");
    }

    function test_WhenCustomFeesAreInvalid() external whenAnOwnerRequestsCustomFees {
        (Presale.Configuration memory pConfig, Membership.Configuration memory mConfig) = composeConfiguration();

        pConfig.fees.tokenADenominator = 0;

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        controller.createPresale(pConfig, new Round[](0), mConfig);
    }

    function test_WhenNotAnOwnerRequestsCustomFees() external {
        vm.startPrank(alice);

        (Presale.Configuration memory pConfig, Membership.Configuration memory mConfig) = composeConfiguration();

        pConfig.fees.tokenANumerator = fees.tokenANumerator - 1;

        // it reverts
        vm.expectRevert(VestController.FeesDontMatch.selector);
        controller.createPresale(pConfig, new Round[](0), mConfig);
    }

    function test_WhenNotAnOwnerRequestsAPresaleWithProtocolFees() external {
        vm.startPrank(alice);

        (Presale.Configuration memory pConfig, Membership.Configuration memory mConfig) = composeConfiguration();

        (address presale,) = controller.createPresale(pConfig, new Round[](0), mConfig);

        Fees memory fees_ = controller.getFees();

        // It sets correct tokenA fee numerator
        assertEq(fees.tokenANumerator, fees_.tokenANumerator);

        // It sets correct tokenA fee denominator
        assertEq(fees.tokenADenominator, fees_.tokenADenominator);

        // It sets correct tokenB fee numerator
        assertEq(fees.tokenBNumerator, fees_.tokenBNumerator);

        // It sets correct tokenB fee denominator
        assertEq(fees.tokenBDenominator, fees_.tokenBDenominator);

        VestPresale instance = VestPresale(payable(presale));

        // It sets correct tokens
        assertEq(address(instance.tokenA()), address(tokenA));
        assertEq(address(instance.tokenB()), address(tokenB));

        // It sets correct claimbackPeriod
        assertEq(instance.claimbackPeriod(), 7 days);
    }
}
