// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Test } from "forge-std/Test.sol";

import { Fees } from "src/types/Fees.sol";
import { Round } from "src/types/Round.sol";
import { Errors } from "src/libraries/Errors.sol";
import { Presale, Membership } from "src/types/Configuration.sol";
import { VestPresale } from "src/VestPresale.sol";
import { VestController } from "src/VestController.sol";
import { CREATE3 } from "lib/solady/src/utils/CREATE3.sol";

import { VestControllerTest } from "test/VestControllerTest.sol";

contract Vest_createPresale is VestControllerTest {
    Fees internal fees = composeFees();

    modifier whenAnOwnerRequestsCustomFees() {
        _;
    }

    function test_WhenCustomFeesAreValid() external whenAnOwnerRequestsCustomFees {
        fees.tokenANumerator = fees.tokenANumerator - 1;
        assertTrue(controller.getFees().tokenANumerator != fees.tokenANumerator, "fees differ");
    }

    function test_WhenCustomFeesAreInvalid() external whenAnOwnerRequestsCustomFees {
        (Presale.Configuration memory pConfig, Membership.Configuration memory mConfig) = composeConfiguration();

        pConfig.fees.tokenADenominator = 0;

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        controller.createPresale(pConfig, new Round[](0), mConfig);
    }

    function test_WhenNotAnOwnerRequestsCustomFees() external {
        vm.startPrank(alice);

        (Presale.Configuration memory pConfig, Membership.Configuration memory mConfig) = composeConfiguration();

        pConfig.fees.tokenANumerator = fees.tokenANumerator - 1;

        // it reverts
        vm.expectRevert(VestController.FeesDontMatch.selector);
        controller.createPresale(pConfig, new Round[](0), mConfig);
    }

    function test_WhenNotAnOwnerRequestsAPresaleWithProtocolFees() external {
        vm.startPrank(alice);

        (Presale.Configuration memory pConfig, Membership.Configuration memory mConfig) = composeConfiguration();

        (address presale,) = controller.createPresale(pConfig, new Round[](0), mConfig);

        Fees memory fees_ = controller.getFees();

        // It sets correct tokenA fee numerator
        assertEq(fees.tokenANumerator, fees_.tokenANumerator);

        // It sets correct tokenA fee denominator
        assertEq(fees.tokenADenominator, fees_.tokenADenominator);

        // It sets correct tokenB fee numerator
        assertEq(fees.tokenBNumerator, fees_.tokenBNumerator);

        // It sets correct tokenB fee denominator
        assertEq(fees.tokenBDenominator, fees_.tokenBDenominator);

        VestPresale instance = VestPresale(payable(presale));

        // It sets correct tokens
        assertEq(address(instance.tokenA()), address(tokenA));
        assertEq(address(instance.tokenB()), address(tokenB));

        // It sets correct claimbackPeriod
        assertEq(instance.claimbackPeriod(), 7 days);
    }

    function test_WhenNoBonusesAreSet() external {
        (Presale.Configuration memory pConfig, Membership.Configuration memory mConfig) = composeConfiguration();
        
        // it allows any valid bonus configuration in rounds
        Round[] memory rounds = new Round[](2);
        rounds[0] = Round({
            name: "Round 1",
            startTimestamp: block.timestamp + 1 hours,
            endTimestamp: block.timestamp + 2 hours,
            whitelistRoot: bytes32(0),
            proofsUri: "",
            bonusNumerator: 1,
            bonusDenominator: 10,
            attributes: ""
        });
        rounds[1] = Round({
            name: "Round 2",
            startTimestamp: block.timestamp + 2 hours,
            endTimestamp: block.timestamp + 3 hours,
            whitelistRoot: bytes32(0),
            proofsUri: "",
            bonusNumerator: 2,
            bonusDenominator: 5,
            attributes: ""
        });

        // Verify presale creation succeeds with valid bonuses
        (address presale,) = controller.createPresale(pConfig, rounds, mConfig);
        assertTrue(presale != address(0));

        // it reverts if round has invalid bonus (denominator=0)
        rounds[0].bonusDenominator = 0;
        vm.expectRevert(CREATE3.InitializationFailed.selector);
        controller.createPresale(pConfig, rounds, mConfig);

        // it succeeds if round has valid bonus (numerator=denominator)
        rounds[0].bonusNumerator = 10;
        rounds[0].bonusDenominator = 10;
        (presale,) = controller.createPresale(pConfig, rounds, mConfig);
        assertTrue(presale != address(0));
    }

    function test_WhenBonusesAreSet() external {
        // Setup allowed bonuses
        controller.addBonusConfig(1, 10); // 10% bonus
        controller.addBonusConfig(2, 5);  // 40% bonus

        (Presale.Configuration memory pConfig, Membership.Configuration memory mConfig) = composeConfiguration();
        
        // it allows rounds with allowed bonus configurations
        Round[] memory rounds = new Round[](2);
        rounds[0] = Round({
            name: "Round 1",
            startTimestamp: block.timestamp + 1 hours,
            endTimestamp: block.timestamp + 2 hours,
            whitelistRoot: bytes32(0),
            proofsUri: "",
            bonusNumerator: 1,
            bonusDenominator: 10,
            attributes: ""
        });
        rounds[1] = Round({
            name: "Round 2",
            startTimestamp: block.timestamp + 2 hours,
            endTimestamp: block.timestamp + 3 hours,
            whitelistRoot: bytes32(0),
            proofsUri: "",
            bonusNumerator: 2,
            bonusDenominator: 5,
            attributes: ""
        });

        // Verify presale creation succeeds with allowed bonuses
        (address presale,) = controller.createPresale(pConfig, rounds, mConfig);
        assertTrue(presale != address(0));

        // it reverts if round has non-allowed bonus
        rounds[0].bonusNumerator = 3;
        rounds[0].bonusDenominator = 10;
        vm.expectRevert(VestController.InvalidBonusConfiguration.selector);
        controller.createPresale(pConfig, rounds, mConfig);

        // it reverts if round has invalid bonus
        rounds[0].bonusNumerator = 11;
        rounds[0].bonusDenominator = 10;
        vm.expectRevert(VestController.InvalidBonusConfiguration.selector);
        controller.createPresale(pConfig, rounds, mConfig);
    }
}
