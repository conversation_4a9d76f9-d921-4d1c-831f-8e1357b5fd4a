// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";

import { Fees } from "src/types/Fees.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestController } from "src/VestController.sol";

import { VestControllerTest } from "test/VestControllerTest.sol";

contract Vest_setFees is VestControllerTest {
    Fees internal fees = Fees({
        tokenANumerator: 0,
        tokenADenominator: 1,
        tokenBNumerator: 10,
        tokenBDenominator: 100,
        nftNumerator: 1_000,
        nftDenominator: 10_000
    });

    function test_WhenTokenBFeeIsGreaterOrEqualThan100Percent() external {
        fees.tokenBNumerator = fees.tokenBDenominator;
        vm.expectRevert(Errors.UnacceptableValue.selector);
        controller.setFees(fees);

        fees.tokenBNumerator = fees.tokenBDenominator + 1;
        vm.expectRevert(Errors.UnacceptableValue.selector);
        controller.setFees(fees);
    }

    function test_WhenTokenAFeeIsGreaterOrEqualThan100Percent() external {
        fees.tokenANumerator = fees.tokenADenominator;
        vm.expectRevert(Errors.UnacceptableValue.selector);
        controller.setFees(fees);

        fees.tokenANumerator = fees.tokenADenominator + 1;
        vm.expectRevert(Errors.UnacceptableValue.selector);
        controller.setFees(fees);
    }

    function test_WhenTokenBFeeDenominatorEquals0() external {
        fees.tokenBDenominator = 0;
        // it reverts because division by 0 is not allowed
        vm.expectRevert(Errors.UnacceptableValue.selector);
        controller.setFees(fees);
    }

    function test_WhenTokenAFeeDenominatorEquals0() external {
        fees.tokenADenominator = 0;
        // it reverts because division by 0 is not allowed
        vm.expectRevert(Errors.UnacceptableValue.selector);
        controller.setFees(fees);
    }

    function test_WhenNotOwnerTryChangeFee() external {
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableUnauthorizedAccount.selector, alice));
        vm.prank(alice);
        controller.setFees(fees);
    }

    function test_WhenFeeStructureIsValid() external {
        // it emits FeesUpdated event
        vm.expectEmit(false, false, false, true);
        emit VestController.FeesUpdated(
            Fees({
                tokenANumerator: 3,
                tokenADenominator: 33,
                tokenBNumerator: 333,
                tokenBDenominator: 3_333,
                nftNumerator: 33_333,
                nftDenominator: 44_444
            })
        );

        controller.setFees(
            Fees({
                tokenANumerator: 3,
                tokenADenominator: 33,
                tokenBNumerator: 333,
                tokenBDenominator: 3_333,
                nftNumerator: 33_333,
                nftDenominator: 44_444
            })
        );

        Fees memory fees_ = controller.getFees();
        // it modifies state variables
        assertEq(fees_.tokenANumerator, 3);
        assertEq(fees_.tokenADenominator, 33);
        assertEq(fees_.tokenBNumerator, 333);
        assertEq(fees_.tokenBDenominator, 3_333);
        assertEq(fees_.nftNumerator, 33_333);
        assertEq(fees_.nftDenominator, 44_444);
    }
}
