// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Vest_setFees
├── when tokenB fee is greater or equal than 100 percent
│   └── it reverts
├── when tokenA fee is greater or equal than 100 percent
│   └── it reverts
├── when tokenB fee denominator equals 0
│   └── it reverts because division by 0 is not allowed
├── when tokenA fee denominator equals 0
│   └── it reverts because division by 0 is not allowed
├── when not owner try change fee
│   └── it reverts
└── when fee structure is valid
    ├── it modifies state variables
    └── it emits FeesUpdated event
