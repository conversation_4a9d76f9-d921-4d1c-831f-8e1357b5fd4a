// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Fees } from "src/types/Fees.sol";

import { VestControllerTest } from "test/VestControllerTest.sol";

contract Vest_getFees is VestControllerTest {
    function test_ReturnsFeesSpecifiedInTheVestProtocol() external {
        Fees memory fees = controller.getFees();

        Fees memory expectedFees = Fees({
            tokenANumerator: 1,
            tokenADenominator: 10,
            tokenBNumerator: 20,
            tokenBDenominator: 100,
            nftNumerator: 500,
            nftDenominator: 10_000
        });

        // it returns Fees specified in the Vest protocol
        assertEq(fees.tokenANumerator, expectedFees.tokenANumerator);
        assertEq(fees.tokenADenominator, expectedFees.tokenADenominator);
        assertEq(fees.tokenBNumerator, expectedFees.tokenBNumerator);
        assertEq(fees.tokenBDenominator, expectedFees.tokenBDenominator);
        assertEq(fees.nftNumerator, expectedFees.nftNumerator);
        assertEq(fees.nftDenominator, expectedFees.nftDenominator);
    }
}
