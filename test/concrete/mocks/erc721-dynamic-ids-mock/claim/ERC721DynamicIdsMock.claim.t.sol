// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { DynamicIdsTest, DynamicIds, ERC721DynamicIds, IERC721Errors, IERC721 } from "test/DynamicIdsTest.sol";

contract ERC721DynamicIdsMock_claim is DynamicIdsTest {
    function setUp() external {
        vm.startPrank(alice);
        id = collection.mint();
    }

    function test_WhenInvalidPublicId() external {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721NonexistentToken.selector, 1));
        collection.claim(1);
        uint256 mintId = DynamicIds.zeroLast16Bytes(id);
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721NonexistentToken.selector, mintId));
        collection.claim(mintId);
        collection.claim(id);
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721NonexistentToken.selector, id));
        collection.claim(id);
    }

    function test_WhenValidPublicId() external {
        uint256 nextPublicId = DynamicIds.createPublicId(id, abi.encodePacked(uint256(1)));
        uint256 mintId = DynamicIds.zeroLast16Bytes(id);
        // it emits Transfer event to indicate burn
        vm.expectEmit(true, true, true, false);
        emit IERC721.Transfer(alice, address(0), id);
        // it emits Transfer event to indicate mint
        vm.expectEmit(true, true, true, false);
        emit IERC721.Transfer(address(0), alice, nextPublicId);
        // emits DynamicIdNFTUpdated event
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, nextPublicId, abi.encodePacked(uint256(1)));
        // it returns new NFT id
        assertEq(collection.claim(id), nextPublicId, "it returns new NFT id");
    }
}
