// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IVestMembership } from "src/IVestMembership.sol";
import { VestMembership } from "src/VestMembership.sol";
import { IVestPresaleScheduler } from "src/IVestPresaleScheduler.sol";

import { VestMembershipTest } from "test/VestMembershipTest.sol";

contract Membership_transferFrom is VestMembershipTest {
    address internal middleman = makeAddr("middleman");

    uint256 internal membershipId;

    function setUp() public {
        membership = fixture();

        vm.warp(vm.unixTime());
    }

    modifier withTradeableMembership() {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 0,
            cliffNumerator: 0,
            cliffDenominator: 0,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);

        vm.prank(alice);
        membership.approve(middleman, membershipId);
        _;
    }

    modifier withNonTradeableMembership() {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 0,
            cliffNumerator: 0,
            cliffDenominator: 0,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 1
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);

        vm.prank(alice);
        membership.approve(middleman, membershipId);
        _;
    }

    function test_WhenTheCallerOwnsTradeableMembership() external withTradeableMembership {
        vm.prank(alice);

        membership.transferFrom(alice, bob, membershipId);

        assertEq(membership.ownerOf(membershipId), bob, "it performs transfer to bob");
    }

    function test_WhenTheCallerOwnsNon_tradeableMembership() external withNonTradeableMembership {
        vm.prank(alice);

        membership.transferFrom(alice, bob, membershipId);

        assertEq(membership.ownerOf(membershipId), bob, "it performs transfer to bob");
    }

    modifier whenTheCallerIsTheMiddleman() {
        vm.startPrank(middleman);
        _;
    }

    function test_GivenMembershipIsNon_tradeable() external withNonTradeableMembership whenTheCallerIsTheMiddleman {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestMembership.TransferNotAllowed.selector, membershipId));
        membership.transferFrom(alice, bob, membershipId);
    }

    function test_WhenListingTimestampEqualsZero() external withTradeableMembership whenTheCallerIsTheMiddleman {
        vm.mockCall(
            scheduler, abi.encodeWithSelector(IVestPresaleScheduler.getListingTimestamp.selector), abi.encode(0)
        );

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestMembership.TransferNotAllowed.selector, membershipId));
        membership.transferFrom(alice, bob, membershipId);
    }

    function test_WhenBlockTimestampIsBeforeListingTimestamp()
        external
        withTradeableMembership
        whenTheCallerIsTheMiddleman
    {
        vm.mockCall(
            scheduler,
            abi.encodeWithSelector(IVestPresaleScheduler.getListingTimestamp.selector),
            abi.encode(block.timestamp + 1)
        );

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestMembership.TransferNotAllowed.selector, membershipId));
        membership.transferFrom(alice, bob, membershipId);
    }

    modifier whenBlockTimestampIsEqualsOfAfterListingTimestamp() {
        vm.mockCall(
            scheduler,
            abi.encodeWithSelector(IVestPresaleScheduler.getListingTimestamp.selector),
            abi.encode(block.timestamp - 1)
        );
        _;
    }

    function test_GivenMembershipIsTradeable()
        external
        withTradeableMembership
        whenTheCallerIsTheMiddleman
        whenBlockTimestampIsEqualsOfAfterListingTimestamp
    {
        membership.transferFrom(alice, bob, membershipId);

        assertEq(membership.ownerOf(membershipId), bob, "it performs transfer to bob");
    }
}
