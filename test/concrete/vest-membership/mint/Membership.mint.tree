// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Membership_mint
├── given the caller is not an owner
│   └── it reverts
└── given the caller is an owner
    ├── given the new owner is address zero
    │   └── it reverts
    ├── given the new owner is an EOA
    │   ├── it returns membership id
    │   ├── it increases total supply
    │   ├── it emits ERC-721 transfer event from zero address
    │   ├── it emits DynamicIdNFTMinted event
    │   ├── it emits DynamicIdNFTUpdated event
    │   ├── it mints a new NFT to a new owner
    │   ├── it assigns that new NFT a correct round
    │   ├── it assigns that new NFT correct attributes
    │   ├── it assigns that new NFT correct usage.max
    │   └── it assigns that new NFT usage.current equal to zero
    └── given the new owner is a smart contract that does not accept the NFT
        └── it reverts
