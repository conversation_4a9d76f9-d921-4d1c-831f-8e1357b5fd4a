// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";
import { IERC721Errors } from "@openzeppelin/contracts/interfaces/draft-IERC6093.sol";
import { IERC721 } from "@openzeppelin/contracts/token/ERC721/IERC721.sol";

import { IVestMembership } from "src/VestMembership.sol";
import { VestMembershipTest } from "test/VestMembershipTest.sol";
import { ERC721DynamicIds } from "src/utils/ERC721DynamicIds.sol";

contract Membership_mint is VestMembershipTest {
    function setUp() external {
        membership = fixture();
    }

    function test_GivenTheCallerIsNotAnOwner() external {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Ownable.OwnableUnauthorizedAccount.selector, alice));
        vm.prank(alice);

        membership.mint(
            alice,
            1,
            1,
            10,
            IVestMembership.Attributes({
                price: 1 ether,
                allocation: 1 ether,
                claimbackPeriod: 1,
                tgeNumerator: 10,
                tgeDenominator: 100,
                cliffDuration: 0,
                cliffNumerator: 0,
                cliffDenominator: 0,
                vestingPeriodCount: 10,
                vestingPeriodDuration: 1 days,
                tradeable: 2
            })
        );
    }

    modifier givenTheCallerIsAnOwner() {
        _;
    }

    function test_GivenTheNewOwnerIsAddressZero() external givenTheCallerIsAnOwner {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721InvalidReceiver.selector, address(0)));

        membership.mint(
            address(0),
            1,
            1,
            10,
            IVestMembership.Attributes({
                price: 1 ether,
                allocation: 1 ether,
                claimbackPeriod: 1,
                tgeNumerator: 10,
                tgeDenominator: 100,
                cliffDuration: 0,
                cliffNumerator: 0,
                cliffDenominator: 0,
                vestingPeriodCount: 10,
                vestingPeriodDuration: 1 days,
                tradeable: 2
            })
        );
    }

    function test_GivenTheNewOwnerIsAnEOA() external givenTheCallerIsAnOwner {
        IVestMembership.Attributes memory test_attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 1 ether,
            claimbackPeriod: 1,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 0,
            cliffNumerator: 0,
            cliffDenominator: 0,
            vestingPeriodCount: 10,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        // it emits ERC-721 transfer event from zero address
        vm.expectEmit(false, false, false, true);
        emit IERC721.Transfer(address(0), alice, 1);

        bytes memory data = abi.encode(IVestMembership.Usage({ current: 1, max: 10 }), 1, test_attributes);

        // it emits DynamicIdNFTMinted event
        vm.expectEmit(false, false, false, true);
        emit ERC721DynamicIds.DynamicIdNFTMinted(1, alice, data);

        // it emits DynamicIdNFTUpdated event
        vm.expectEmit(false, false, false, false);
        bytes memory payload = abi.encodePacked("1");
        emit ERC721DynamicIds.DynamicIdNFTUpdated(1, 1, payload);

        // it returns membership id
        uint256 newMembershipId = membership.mint(alice, 1, 1, 10, test_attributes);

        // it mints a new NFT to a new owner
        assertEq(membership.ownerOf(newMembershipId), alice);

        // it assigns that new NFT a correct round
        assertEq(membership.getRoundId(newMembershipId), 1);

        // it assigns that new NFT correct attributes
        IVestMembership.Attributes memory attributes = membership.getAttributes(newMembershipId);
        assertEq(attributes.allocation, 1 ether, "Membership allocation equals allocation set during mint");
        assertEq(attributes.price, 1 ether, "Membership price equals price set during mint");
        assertEq(attributes.cliffDuration, 0, "Membership cliffDuration equals cliffDuration set during mint");
        assertEq(attributes.cliffNumerator, 0, "Membership cliffNumerator equals cliffNumerator set during mint");
        assertEq(attributes.cliffDenominator, 0, "Membership cliffDenominator equals cliffDenominator set during mint");
        assertEq(attributes.claimbackPeriod, 1, "Membership claimbackPeriod equals claimbackPeriod set during mint");
        assertEq(attributes.tgeNumerator, 10, "Membership tgeNumerator equals tgeNumerator set during mint");
        assertEq(attributes.tgeDenominator, 100, "Membership tgeDenominator equals tgeDenominator set during mint");
        assertEq(
            attributes.vestingPeriodCount, 10, "Membership vestingPeriodCount equals vestingPeriodCount set during mint"
        );
        assertEq(
            attributes.vestingPeriodDuration,
            1 days,
            "Membership vestingPeriodDuration equals vestingPeriodDuration set during mint"
        );

        IVestMembership.Usage memory usage = membership.getUsage(newMembershipId);

        // it assigns that new NFT correct usage.max
        assertEq(usage.max, 10);

        // it assigns that new NFT usage.current equal to zero
        assertEq(usage.current, 1); // TODO clarify why usage.current should be equal to zero
    }

    function test_GivenTheNewOwnerIsASmartContractThatDoesNotAcceptTheNFT() external givenTheCallerIsAnOwner {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721InvalidReceiver.selector, address(membership)));

        membership.mint(
            address(membership),
            1,
            1,
            10,
            IVestMembership.Attributes({
                price: 1 ether,
                allocation: 1 ether,
                claimbackPeriod: 1,
                tgeNumerator: 10,
                tgeDenominator: 100,
                cliffDuration: 0,
                cliffNumerator: 0,
                cliffDenominator: 0,
                vestingPeriodCount: 10,
                vestingPeriodDuration: 1 days,
                tradeable: 2
            })
        );
    }
}
