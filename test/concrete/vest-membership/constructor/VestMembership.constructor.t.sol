// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Membership } from "src/types/Configuration.sol";
import { IVestFeeCollectorProvider } from "src/IVestFeeCollectorProvider.sol";

import { VestMembershipTest } from "test/VestMembershipTest.sol";

contract Membership_constructor is VestMembershipTest {
    address internal feeCollector = makeAddr("feeCollector");

    function setUp() public {
        membership = fixture();
    }

    function test_SetsPresaleAsAnOwner() external {
        assertEq(membership.owner(), address(this), "it sets presale as an owner");
    }

    function test_InitializesNFTRoyaltiesWithACorrectPresaleAddress() external {
        vm.mockCall(
            feeCollectorProvider,
            abi.encodeWithSelector(IVestFeeCollectorProvider.getFeeCollector.selector),
            abi.encode(feeCollector)
        );

        Membership.Fees memory fees = composeFees();

        // it initializes NFTRoyalties with a correct presale address
        (address feeCollector_, uint256 royaltyAmount) = membership.royaltyInfo(0, 1 ether);

        assertEq(feeCollector_, feeCollector, "it returns feeCollector");
        assertEq(
            royaltyAmount,
            (1 ether * fees.numerator) / fees.denominator,
            "it returns royaltyAmount based on presale parameters"
        );
    }

    function test_InitializesERC721WithTheCorrectNameAndSymbol() external {
        assertEq(membership.name(), "ERC20Sample Vesting", "it initializes ERC721 with the correct name");
        assertEq(membership.symbol(), "vMCK", "it initializes ERC721 with the correct symbol");
    }
}
