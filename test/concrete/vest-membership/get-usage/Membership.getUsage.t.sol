// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC721Errors } from "@openzeppelin/contracts/interfaces/draft-IERC6093.sol";

import { IVestMembership } from "src/VestMembership.sol";

import { VestMembershipTest } from "test/VestMembershipTest.sol";

contract Membership_getUsage is VestMembershipTest {
    uint256 internal membershipId;

    function setUp() public {
        membership = fixture();

        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 0,
            cliffNumerator: 0,
            cliffDenominator: 0,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);
    }

    function test_GivenTheMembershipDoesNotExist() external {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721NonexistentToken.selector, 0));
        membership.getUsage(0);
    }

    function test_GivenTheMembershipExists() external {
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        assertEq(usage.max, 1 ether, "it returns max usage");
        assertEq(usage.current, 0, "it returns current usage");
    }
}
