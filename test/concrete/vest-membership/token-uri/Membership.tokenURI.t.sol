// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Strings } from "@openzeppelin/contracts/utils/Strings.sol";
import { IERC721Errors } from "@openzeppelin/contracts/interfaces/draft-IERC6093.sol";

import { IVestMembership } from "src/VestMembership.sol";
import { IVestPresaleScheduler } from "src/IVestPresaleScheduler.sol";

import { VestMembershipTest } from "test/VestMembershipTest.sol";
import { FileSnapshotTest } from "test/FileSnapshotTest.sol";
import { Base64 } from "solady/utils/Base64.sol";
import { strings } from "solidity-stringutils/strings.sol";

contract Membership_tokenURI is VestMembershipTest, FileSnapshotTest {
    using strings for *;

    uint256 internal membershipId;

    uint256 internal constant START_TIMESTAMP = 1708124317865;

    function setUp() public {
        membership = fixture();

        vm.mockCall(
            scheduler,
            abi.encodeWithSelector(IVestPresaleScheduler.getTgeTimestamp.selector),
            abi.encode(START_TIMESTAMP)
        );
    }

    function test_GivenAnNFTDoesNotExist() external {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721NonexistentToken.selector, 0));
        membership.tokenURI(0);
    }

    function test_GivenAnNFTDoesExist() external fileSnapshotTest {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 0,
            cliffNumerator: 0,
            cliffDenominator: 0,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 100 ether, attributes);

        // it returns tokenURI
        string memory tokenURI = membership.tokenURI(membershipId);

        string memory metadataJSON = _decodeBase64Data(tokenURI, "data:application/json;base64,");
        addFileSnapshot("membership-metadata.json", metadataJSON);

        string memory image = vm.parseJsonString(metadataJSON, "$.image");
        string memory imageSvg = _decodeBase64Data(image, "data:image/svg+xml;base64,");
        addFileSnapshot("membership-image.svg", imageSvg);

        strings.slice memory metadata = metadataJSON.toSlice();

        assertTrue(metadata.contains("Vesting of MCK".toSlice()), "it specifies NFT name 'Vesting of ${token symbol}'");

        assertTrue(
            metadata.contains('"trait_type":"Usage","display_type":"boost_percentage","value":0'.toSlice()),
            "it specifies 'usage' numeric attribute with max_value 100'"
        );

        assertTrue(
            metadata.contains('"trait_type":"Vested tokens","display_type":"number","value":100'.toSlice()),
            "it specifies 'vested tokens' numeric attribute rounded down without decimals"
        );

        assertTrue(
            metadata.contains(
                string.concat(
                    '"trait_type":"Vesting start","display_type":"date","value":', Strings.toString(START_TIMESTAMP)
                ).toSlice()
            ),
            "it specifies 'vesting start' date attribute"
        );

        uint256 endTimestamp = START_TIMESTAMP + attributes.cliffDuration
            + (attributes.vestingPeriodCount * attributes.vestingPeriodDuration);

        assertTrue(
            metadata.contains(
                string.concat(
                    '"trait_type":"Vesting end","display_type":"date","value":', Strings.toString(endTimestamp)
                ).toSlice()
            ),
            "it specifies 'vesting end' date attribute"
        );

        assertTrue(
            metadata.contains('"trait_type":"Vested token name","value":"ERC20Sample"'.toSlice()),
            "it specifies 'vested token name' text attribute"
        );

        assertTrue(
            metadata.contains('"trait_type":"Vested token symbol","value":"MCK"'.toSlice()),
            "it specifies 'vested token symbol' text attribute"
        );

        assertTrue(
            metadata.contains(
                string.concat(
                    '"trait_type":"Vested token address","value":"', Strings.toHexString(uint160(address(token)), 20)
                ).toSlice()
            ),
            "it specifies 'vested token address' text attribute"
        );

        assertTrue(
            metadata.contains('"trait_type":"TGE","display_type":"boost_percentage","value":10'.toSlice()),
            "it specifies 'tge' rounded down boost_percentage attribute"
        );

        assertTrue(
            metadata.contains('trait_type":"Cliff duration","value":"no cliff"'.toSlice()),
            "it specifies 'cliff duration' text attribute"
        );

        assertTrue(
            metadata.contains('"trait_type":"Cliff unlock","display_type":"boost_percentage","value":0'.toSlice()),
            "it specifies 'cliff unlock' rounded down boost_percentage attribute"
        );

        assertTrue(
            metadata.contains('"trait_type":"Unlock frequency","value":"every day"'.toSlice()),
            "it specifies 'unlock frequency' text attribute"
        );

        assertTrue(
            metadata.contains('"trait_type":"Unlock frequency","value":"every day"'.toSlice()),
            "it specifies 'unlock frequency' text attribute"
        );

        assertTrue(metadata.contains('"description":"Example description"'.toSlice()), "it specifies a description");

        assertTrue(metadata.contains(string.concat('"image":"', image).toSlice()), "it specifies a svg image");

        // TODO: it specifies 'allocation' numeric attribute rounded down without decimals
        // TODO: it specifies 'claimableBack' text attribute 'True' or 'False'
        // TODO: it specifies 'claimback end at' date attribute
        // TODO: it specifies 'price' text attribute equal to '0.03 USDT'
        // TODO: it specifies 'roundId' numeric attribute
        // TODO: it specifies 'vesting contract address' text attribute
    }

    function test_GivenAnNFTHasNoCliff() external {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 0,
            cliffNumerator: 0,
            cliffDenominator: 0,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);

        string memory tokenURI = membership.tokenURI(membershipId);

        string memory metadataJSON = _decodeBase64Data(tokenURI, "data:application/json;base64,");

        strings.slice memory metadata = metadataJSON.toSlice();

        assertTrue(
            metadata.contains('"trait_type":"Cliff duration","value":"no cliff"'.toSlice()),
            "it specifies 'cliff duration' text attribute equal to 'no cliff'"
        );

        assertTrue(
            metadata.contains('"trait_type":"Cliff unlock","display_type":"boost_percentage","value":0'.toSlice()),
            "it specifies 'cliff unlock' boost_percentage attribute equal zero"
        );
    }

    function test_GivenAnNFTHasA90DaysFlatCliff() external {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 90 days,
            cliffNumerator: 0,
            cliffDenominator: 0,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);

        string memory tokenURI = membership.tokenURI(membershipId);

        string memory metadataJSON = _decodeBase64Data(tokenURI, "data:application/json;base64,");

        strings.slice memory metadata = metadataJSON.toSlice();

        assertTrue(
            metadata.contains('"trait_type":"Cliff duration","value":"90 days"'.toSlice()),
            "it specifies 'cliff duration' text attribute equal to '90 days'"
        );

        assertTrue(
            metadata.contains('"trait_type":"Cliff unlock","display_type":"boost_percentage","value":0'.toSlice()),
            "it specifies 'cliff unlock' boost_percentage attribute equal zero"
        );
    }

    function test_GivenAnNFTHasA90DaysCliffWith20PercentCliffUnlock() external {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 90 days,
            cliffNumerator: 2,
            cliffDenominator: 10,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);

        string memory tokenURI = membership.tokenURI(membershipId);

        string memory metadataJSON = _decodeBase64Data(tokenURI, "data:application/json;base64,");

        strings.slice memory metadata = metadataJSON.toSlice();

        assertTrue(
            metadata.contains('"trait_type":"Cliff unlock","display_type":"boost_percentage","value":20'.toSlice()),
            "it specifies 'cliff unlock' boost_percentage attribute equal 20"
        );
    }

    function test_GivenAnNFTHasNoVestingPeriodCount() external {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 90 days,
            cliffNumerator: 2,
            cliffDenominator: 10,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 0,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);

        string memory tokenURI = membership.tokenURI(membershipId);

        string memory metadataJSON = _decodeBase64Data(tokenURI, "data:application/json;base64,");

        strings.slice memory metadata = metadataJSON.toSlice();

        assertTrue(
            metadata.contains('"trait_type":"Unlock frequency","value":"none"'.toSlice()),
            "it specifies 'unlock frequency' text attribute to 'none'"
        );
    }

    function test_GivenAnNFTHasVestingPeriodDurationEqualOneHour() external {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 90 days,
            cliffNumerator: 2,
            cliffDenominator: 10,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 hours,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);

        string memory tokenURI = membership.tokenURI(membershipId);

        string memory metadataJSON = _decodeBase64Data(tokenURI, "data:application/json;base64,");

        strings.slice memory metadata = metadataJSON.toSlice();

        assertTrue(
            metadata.contains('"trait_type":"Unlock frequency","value":"every hour"'.toSlice()),
            "it specifies 'unlock frequency' text attribute to 'every hour'"
        );
    }

    function test_GivenAnNFTHasVestingPeriodDurationEqual3Hours() external {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 90 days,
            cliffNumerator: 2,
            cliffDenominator: 10,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 3 hours,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);

        string memory tokenURI = membership.tokenURI(membershipId);

        string memory metadataJSON = _decodeBase64Data(tokenURI, "data:application/json;base64,");

        strings.slice memory metadata = metadataJSON.toSlice();

        assertTrue(
            metadata.contains('"trait_type":"Unlock frequency","value":"every 3 hours"'.toSlice()),
            "it specifies 'unlock frequency' text attribute to 'every 3 hours'"
        );
    }

    function test_GivenAnNFTHasVestingPeriodDurationEqualOneDay() external {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 90 days,
            cliffNumerator: 2,
            cliffDenominator: 10,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);

        string memory tokenURI = membership.tokenURI(membershipId);

        string memory metadataJSON = _decodeBase64Data(tokenURI, "data:application/json;base64,");

        strings.slice memory metadata = metadataJSON.toSlice();

        assertTrue(
            metadata.contains('"trait_type":"Unlock frequency","value":"every day"'.toSlice()),
            "it specifies 'unlock frequency' text attribute to 'every day'"
        );
    }

    function test_GivenAnNFTHasVestingPeriodDurationEqual30Days() external {
        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 90 days,
            cliffNumerator: 2,
            cliffDenominator: 10,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 30 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);

        string memory tokenURI = membership.tokenURI(membershipId);

        string memory metadataJSON = _decodeBase64Data(tokenURI, "data:application/json;base64,");

        strings.slice memory metadata = metadataJSON.toSlice();

        assertTrue(
            metadata.contains('"trait_type":"Unlock frequency","value":"every 30 days"'.toSlice()),
            "it specifies 'unlock frequency' text attribute to 'every 30 days'"
        );
    }

    function _decodeBase64Data(string memory str, string memory prefix) internal returns (string memory) {
        strings.slice memory strSlice = str.toSlice();
        strings.slice memory prefixSlice = prefix.toSlice();
        assertTrue(strSlice.startsWith(prefixSlice));
        return string(Base64.decode(strSlice.beyond(prefixSlice).toString()));
    }
}
