// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Membership_tokenURI
├── given an NFT does not exist
│   └── it reverts
├── given an NFT does exist
│   ├── it specifies NFT name 'Vesting of ${token symbol}'
│   ├── it specifies 'usage' numeric attribute with max_value 100
│   ├── it specifies 'vested tokens' numeric attribute rounded down without decimals
│   ├── it specifies 'vesting start' date attribute
│   ├── it specifies 'vesting end' date attribute
│   ├── it specifies 'vested token name' text attribute
│   ├── it specifies 'vested token symbol' text attribute
│   ├── it specifies 'vested token address' text attribute
│   ├── it specifies 'tge' rounded down boost_percentage attribute
│   ├── it specifies 'cliff duration' text attribute
│   ├── it specifies 'cliff unlock' rounded down boost_percentage attribute
│   ├── it specifies 'vesting contract address' text attribute
│   ├── it specifies 'unlock frequency' text attribute
│   ├── it specifies 'roundId' numeric attribute
│   ├── it specifies 'refundable' text attribute 'True' or 'False'
│   ├── it specifies 'refunds end at' date attribute
│   ├── it specifies 'price' text attribute equal to '0.03 USDT'
│   ├── it specifies 'allocation' numeric attribute rounded down without decimals
│   ├── it specifies a description
│   └── it specifies a svg image
├── given an NFT has no cliff
│   ├── it specifies 'cliff duration' text attribute equal to 'no cliff'
│   └── it specifies 'cliff unlock' boost_percentage attribute equal zero
├── given an NFT has a 90 days flat cliff
│   ├── it specifies 'cliff duration' text attribute equal to '90 days'
│   └── it specifies 'cliff unlock' boost_percentage attribute equal zero
├── given an NFT has a 90 days cliff with 20 percent cliff unlock
│   └── it specifies 'cliff unlock' boost_percentage attribute equal 20
├── given an NFT has no vesting period count
│   └── it specifies 'unlock frequency' text attribute to 'none'
├── given an NFT has vesting period duration equal one hour
│   └── it specifies 'unlock frequency' text attribute to 'every hour'
├── given an NFT has vesting period duration equal 3 hours
│   └── it specifies 'unlock frequency' text attribute to 'every 3 hours'
├── given an NFT has vesting period duration equal one day
│   └── it specifies 'unlock frequency' text attribute to 'every day'
└── given an NFT has vesting period duration equal 30 days
    └── it specifies 'unlock frequency' text attribute to 'every 30 days'
