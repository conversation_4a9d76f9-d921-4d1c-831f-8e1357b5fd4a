// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC721Errors } from "@openzeppelin/contracts/interfaces/draft-IERC6093.sol";

import { IVestMembership } from "src/VestMembership.sol";

import { VestMembershipTest } from "test/VestMembershipTest.sol";

contract Membership_getAttributes is VestMembershipTest {
    uint256 internal membershipId;

    IVestMembership.Attributes internal attributes;

    function setUp() public {
        membership = fixture();

        attributes = IVestMembership.Attributes({
            price: 1 ether,
            allocation: 100 ether,
            claimbackPeriod: 0,
            tgeNumerator: 10,
            tgeDenominator: 100,
            cliffDuration: 0,
            cliffNumerator: 0,
            cliffDenominator: 0,
            vestingPeriodCount: 360,
            vestingPeriodDuration: 1 days,
            tradeable: 2
        });

        membershipId = membership.mint(alice, 0, 0, 1 ether, attributes);
    }

    function test_GivenAMembershipDoesNotExist() external {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721NonexistentToken.selector, 0));
        membership.getAttributes(0);
    }

    function test_GivenAMembershipExists() external {
        // it returns attributes
        IVestMembership.Attributes memory attributes_ = membership.getAttributes(membershipId);

        assertEq(attributes_.price, attributes.price, "returned attributes.price equals declared price");
        assertEq(
            attributes_.allocation, attributes.allocation, "returned attributes.allocation equals declared allocation"
        );
        assertEq(
            attributes_.claimbackPeriod,
            attributes.claimbackPeriod,
            "returned attributes.claimbackPeriod equals declared claimbackPeriod"
        );
        assertEq(
            attributes_.tgeNumerator,
            attributes.tgeNumerator,
            "returned attributes.tgeNumerator equals declared tgeNumerator"
        );
        assertEq(
            attributes_.cliffDuration,
            attributes.cliffDuration,
            "returned attributes.cliffDuration equals declared cliffDuration"
        );
        assertEq(
            attributes_.cliffNumerator,
            attributes.cliffNumerator,
            "returned attributes.cliffNumerator equals declared cliffNumerator"
        );
        assertEq(
            attributes_.cliffDenominator,
            attributes.cliffDenominator,
            "returned attributes.cliffDenominator equals declared cliffDenominator"
        );
        assertEq(
            attributes_.vestingPeriodCount,
            attributes.vestingPeriodCount,
            "returned attributes.vestingPeriodCount equals declared vestingPeriodCount"
        );
        assertEq(
            attributes_.vestingPeriodDuration,
            attributes.vestingPeriodDuration,
            "returned attributes.vestingPeriodDuration equals declared vestingPeriodDuration"
        );
    }
}
