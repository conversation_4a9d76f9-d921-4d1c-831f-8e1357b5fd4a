// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { VestMembershipTest } from "test/VestMembershipTest.sol";

contract Membership_owner is VestMembershipTest {
    function test_ReturnsAnAddressOfTheCurrentOwner() external {
        membership = fixture(); // it creates a new membership

        address expected_owner = membership.owner();

        // it returns an address of the current owner
        assertEq(expected_owner, address(this));
    }
}
