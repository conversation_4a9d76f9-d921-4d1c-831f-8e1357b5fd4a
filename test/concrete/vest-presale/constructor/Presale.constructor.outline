Presale_constructor
  when tokenA is missing
    it reverts
  when tokenB is missing
    it reverts
  when manager is missing
    it reverts
  when beneficiary is missing
    it reverts
  when rounds is an empty array
    it succeeds
  when a round name is missing
    it reverts
  when a round startTimestamp is missing
    it reverts
  when a round endTimestamp is missing
    it reverts
  when tgeTimestamp zero and listingTimestamp is non zero
    it reverts
  when tgeTimestamp is greater than listingTimestamp and listingTimestamp is non zero
    it reverts
  when tgeTimestamp is greater than listingTimestamp and listingTimestamp equals zero
    it saves tgeTimestamp
  when tgeTimestamp is zero
    it configures Membership tgeTimestamp to zero
  when parameters are correct
    it creates a presale permissionlessly
    it saves tokenA address
    it saves tokenB address
    it saves manager address
    it saves feeCollector address
    it saves beneficiary address
    it saves listingTimestamp
    it saves refundsPeriod
    it saves rounds with their names, startTimestamp, endTimestamp, whitelistRoot, and proofsUri
    it emits RoundUpdated event with indexed round id
    it creates Membership
    it configures Membership owner to itself (the Presale)
    it configures Membership name after tokenA name
    it configures Membership symbol after tokenA symbol
    it configures Membership royalty receiver to feeCollector
    it configures Membership royalty according to fees
    it configures Membership tgeTimestamp to one hour before the listing
    it creates Membership
    it configures Membership owner to itself (the Presale)
    it configures Membership name after tokenA name
    it configures Membership symbol after tokenA symbol
    it configures Membership royalty receiver to feeCollector
    it configures Membership royalty according to fees
    it configures Membership tgeTimestamp to one hour before the listing
