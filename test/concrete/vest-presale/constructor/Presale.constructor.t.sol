// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";

import { Round, RoundState } from "src/types/Round.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { IVestFeeCollectorProvider } from "src/IVestFeeCollectorProvider.sol";

import { VestTest, Configuration } from "test/VestTest.sol";

contract Presale_constructor is VestTest {
    function test_WhenTokenAIsMissing() external {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.tokenA = IERC20(address(0));

        vm.expectRevert(Errors.UnacceptableReference.selector);
        new VestPresale(
            IVestMembership(makeAddr("membership")),
            IVestFeeCollectorProvider(makeAddr("feeCollectorProvider")),
            configuration.presale,
            new Round[](0)
        );
    }

    function test_WhenTokenBIsMissing() external {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.tokenB = IERC20(address(0));

        vm.expectRevert(Errors.UnacceptableReference.selector);
        new VestPresale(
            IVestMembership(makeAddr("membership")),
            IVestFeeCollectorProvider(makeAddr("feeCollectorProvider")),
            configuration.presale,
            new Round[](0)
        );
    }

    function test_WhenManagerIsMissing() external {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.manager = address(0);

        vm.expectRevert(Errors.UnacceptableReference.selector);
        new VestPresale(
            IVestMembership(makeAddr("membership")),
            IVestFeeCollectorProvider(makeAddr("feeCollectorProvider")),
            configuration.presale,
            new Round[](0)
        );
    }

    function test_WhenBeneficiaryIsMissing() external {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.beneficiary = address(0);

        vm.expectRevert(Errors.UnacceptableReference.selector);
        new VestPresale(
            IVestMembership(makeAddr("membership")),
            IVestFeeCollectorProvider(makeAddr("feeCollectorProvider")),
            configuration.presale,
            new Round[](0)
        );
    }

    function test_WhenRoundsIsAnEmptyArray() external {
        Configuration memory configuration = composeConfiguration();

        // it succeeds
        presale = fixture(configuration, new Round[](0));

        (uint256[] memory ids, Round[] memory rounds, RoundState[] memory states) = presale.getRounds();

        assertEq(ids.length, 0, "ids array is empty");
        assertEq(rounds.length, 0, "rounds array is empty");
        assertEq(states.length, 0, "states array is empty");
    }

    function test_WhenARoundNameIsMissing() external {
        Configuration memory configuration = composeConfiguration();

        Round[] memory rounds = new Round[](1);

        rounds[0] = Round({
            name: "",
            startTimestamp: vm.unixTime(),
            endTimestamp: vm.unixTime() * 2,
            whitelistRoot: bytes32(0),
            proofsUri: ""
        });

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestPresale(
            IVestMembership(makeAddr("membership")),
            IVestFeeCollectorProvider(makeAddr("feeCollectorProvider")),
            configuration.presale,
            rounds
        );
    }

    function test_WhenARoundStartTimestampIsMissing() external {
        Configuration memory configuration = composeConfiguration();

        Round[] memory rounds = new Round[](1);

        rounds[0] = Round({
            name: "ROUND",
            startTimestamp: 0,
            endTimestamp: vm.unixTime() * 2,
            whitelistRoot: bytes32(0),
            proofsUri: ""
        });

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestPresale(
            IVestMembership(makeAddr("membership")),
            IVestFeeCollectorProvider(makeAddr("feeCollectorProvider")),
            configuration.presale,
            rounds
        );
    }

    function test_WhenARoundEndTimestampIsMissing() external {
        Configuration memory configuration = composeConfiguration();

        Round[] memory rounds = new Round[](1);

        rounds[0] = Round({
            name: "ROUND",
            startTimestamp: vm.unixTime(),
            endTimestamp: 0,
            whitelistRoot: bytes32(0),
            proofsUri: ""
        });

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestPresale(
            IVestMembership(makeAddr("membership")),
            IVestFeeCollectorProvider(makeAddr("feeCollectorProvider")),
            configuration.presale,
            rounds
        );
    }

    function test_WhenTgeTimestampZeroAndListingTimestampIsNonZero() external {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.tgeTimestamp = 0;
        configuration.presale.listingTimestamp = 1;

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestPresale(
            IVestMembership(makeAddr("membership")),
            IVestFeeCollectorProvider(makeAddr("feeCollectorProvider")),
            configuration.presale,
            new Round[](0)
        );
    }

    function test_WhenTgeTimestampIsGreaterThanListingTimestampAndListingTimestampIsNonZero() external {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.listingTimestamp = 1;
        configuration.presale.tgeTimestamp = configuration.presale.listingTimestamp + 1;

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        new VestPresale(
            IVestMembership(makeAddr("membership")),
            IVestFeeCollectorProvider(makeAddr("feeCollectorProvider")),
            configuration.presale,
            new Round[](0)
        );
    }

    function test_WhenTgeTimestampIsGreaterThanListingTimestampAndListingTimestampEqualsZero() external {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.tgeTimestamp = 1;
        configuration.presale.listingTimestamp = 0;

        presale = fixture(configuration);

        assertEq(presale.getTgeTimestamp(), configuration.presale.tgeTimestamp, "it saves tgeTimestamp");
    }

    function test_WhenTgeTimestampIsZero() external {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.tgeTimestamp = 0;
        configuration.presale.listingTimestamp = 0;

        presale = fixture(configuration, new Round[](0));

        // it configures Membership tgeTimestamp to zero
        assertEq(membership.getStartTimestamp(), 0, "membership tgeTimestamp equals zero");
    }

    function test_WhenParametersAreCorrect() external {
        Round[] memory rounds = new Round[](1);

        rounds[0] = Round({
            name: "ROUND",
            startTimestamp: vm.unixTime(),
            endTimestamp: vm.unixTime() * 2,
            whitelistRoot: bytes32(0),
            proofsUri: ""
        });
        Configuration memory configuration = composeConfiguration();
        vm.expectEmit(true, false, false, false);
        emit VestPresale.RoundUpdated(1);
        presale = fixture(configuration, rounds);
        assertEq(address(presale.tokenA()), address(configuration.presale.tokenA), "it saves tokenA address");
        assertEq(address(presale.tokenB()), address(configuration.presale.tokenB), "it saves tokenB address");
        assertEq(presale.manager(), configuration.presale.manager, "it saves manager address");
        assertEq(presale.getFeeCollector(), controller.owner(), "it saves feeCollector address");
        assertEq(presale.beneficiary(), configuration.presale.beneficiary, "it saves beneficiary address");
        assertEq(presale.getListingTimestamp(), configuration.presale.listingTimestamp, "it saves listingTimestamp");
        assertEq(presale.claimbackPeriod(), configuration.presale.claimbackPeriod, "it saves claimbackPeriod");

        // it saves rounds with their names, startTimestamp, endTimestamp, whitelistRoot, and proofsUri
        (, Round[] memory rounds_,) = presale.getRounds();
        assertEq(rounds_[0].name, rounds[0].name, "it saves rounds with their names");
        assertEq(rounds_[0].startTimestamp, rounds[0].startTimestamp, "it saves rounds with their startTimestamp");
        assertEq(rounds_[0].endTimestamp, rounds[0].endTimestamp, "it saves rounds with their endTimestamp");
        assertEq(rounds_[0].whitelistRoot, rounds[0].whitelistRoot, "it saves rounds with their whitelistRoot");
        assertEq(rounds_[0].proofsUri, rounds[0].proofsUri, "it saves rounds with their proofsUri");

        assertNotEq(address(presale.membership()), address(0), "it creates Membership");
        assertEq(membership.owner(), address(presale), "it configures Membership owner to itself (the Presale)");

        // it configures Membership name after tokenA name
        // it configures Membership symbol after tokenA symbol

        (address sRoyaltyReceiver, uint256 sRoyaltyAmount) = membership.royaltyInfo(0, 1 ether);

        assertEq(
            sRoyaltyReceiver, presale.getFeeCollector(), "it configures Membership royalty receiver to feeCollector"
        );
        assertEq(sRoyaltyAmount, 1 ether / 100, "it configures Membership royalty amount according to fees");

        assertEq(
            membership.getStartTimestamp(), configuration.presale.tgeTimestamp, "it configures Membership tgeTimestamp"
        );

        assertNotEq(address(presale.membership()), address(0), "it creates Membership");
        assertEq(membership.owner(), address(presale), "it configures Membership owner to itself (the Presale)");

        // it configures Membership name after tokenA name
        // it configures Membership symbol after tokenA symbol

        (address vRoyaltyReceiver, uint256 vRoyaltyAmount) = membership.royaltyInfo(0, 1 ether);

        assertEq(
            vRoyaltyReceiver, presale.getFeeCollector(), "it configures Membership royalty receiver to feeCollector"
        );
        assertEq(vRoyaltyAmount, 1 ether / 100, "it configures Membership royalty amount according to fees");

        assertEq(
            membership.getStartTimestamp(), configuration.presale.tgeTimestamp, "it configures Membership tgeTimestamp"
        );
    }
}
