// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { Round } from "src/types/Round.sol";
import { VestPresale } from "src/VestPresale.sol";
import { VestTest } from "test/VestTest.sol";

contract Presale_addRound is VestTest {
    Round validRound;

    function setUp() public override {
        super.setUp();

        fixture();
    }

    function test_WhenInvalidRoundConfig() external {
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        vm.prank(manager);
        uint256 timestamp = vm.unixTime();
        presale.addRound(
            Round({
                name: "invalid round",
                startTimestamp: timestamp,
                endTimestamp: timestamp - 1,
                whitelistRoot: "",
                proofsUri: ""
            })
        );
    }

    modifier whenValidRoundConfig() {
        validRound = Round({
            name: "valid round",
            startTimestamp: vm.unixTime(),
            endTimestamp: vm.unixTime() + 1,
            whitelistRoot: "",
            proofsUri: "https://example.com"
        });
        _;
        delete validRound;
    }

    function test_WhenTheCallerIsAPresaleManager() external whenValidRoundConfig {
        vm.startPrank(manager);

        // it emits a {RoundUpdated} event
        vm.expectEmit(true, false, false, false);
        emit VestPresale.RoundUpdated(1);

        // it adds round
        presale.addRound(validRound);
        Round memory addedRound = presale.getRound(1);
        // it assigns round name
        assertEq(validRound.name, addedRound.name);
        // it assigns round startTimestamp
        assertEq(validRound.startTimestamp, addedRound.startTimestamp);
        // it assigns round endTimestamp
        assertEq(validRound.endTimestamp, addedRound.endTimestamp);
        // it assigns round whitelistRoot
        assertEq(validRound.whitelistRoot, addedRound.whitelistRoot);
        // it assigns round proofsURI
        assertEq(validRound.proofsUri, addedRound.proofsUri);
        // it increases next round id sequencer
        vm.expectEmit(true, false, false, false);
        emit VestPresale.RoundUpdated(2);
        presale.addRound(validRound);
    }

    function test_WhenTheCallerIsAnAttacker() external whenValidRoundConfig {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));
        vm.prank(chuck);
        presale.addRound(validRound);
    }
}
