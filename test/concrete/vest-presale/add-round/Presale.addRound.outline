Presale_addRound
  when invalid round config
    it reverts
  when valid round config
    when the caller is a presale manager
      it adds round
      it emits a {RoundUpdated} event
      it increases next round id sequencer
      it assigns round name
      it assigns round startTimestamp
      it assigns round endTimestamp
      it assigns round whitelistRoot
      it assigns round proofsURI
    when the caller is an attacker
      it reverts
