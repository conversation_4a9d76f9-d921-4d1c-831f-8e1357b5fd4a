// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { Errors } from "src/libraries/Errors.sol";
import { Round } from "src/types/Round.sol";
import { VestTest } from "test/VestTest.sol";

contract Presale_updateRound is VestTest {
    uint256 roundId;

    function setUp() public override {
        super.setUp();

        fixture(composeRounds());
    }

    function test_WhenTheCallerIsNotAManager() external {
        (uint256[] memory ids,,) = presale.getRounds();

        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));

        Round memory parameters =
            Round({ name: "ROUND X", startTimestamp: 0, endTimestamp: 0, whitelistRoot: "", proofsUri: "" });

        presale.updateRound(ids[0], parameters);
    }

    modifier whenTheCallerIsAManager() {
        vm.startPrank(manager);
        _;
    }

    function test_GivenTheRoundDoesNotExist() external whenTheCallerIsAManager {
        (uint256[] memory ids,,) = presale.getRounds();

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);

        Round memory parameters =
            Round({ name: "ROUND X", startTimestamp: 0, endTimestamp: 0, whitelistRoot: "", proofsUri: "" });

        presale.updateRound(ids[ids.length - 1] + 1, parameters);
    }

    modifier givenTheRoundExists() {
        (uint256[] memory ids,,) = presale.getRounds();

        roundId = ids[0];
        _;
    }

    function test_WhenStartTimestampEqualToOrGreaterThanEndTimestamp()
        external
        whenTheCallerIsAManager
        givenTheRoundExists
    {
        Round memory parameters =
            Round({ name: "ROUND", startTimestamp: 1, endTimestamp: 1, whitelistRoot: "", proofsUri: "" });

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);

        presale.updateRound(roundId, parameters);
    }

    modifier whenValidRoundConfig() {
        _;
    }

    function test_WhenRoundStartsInLessThanAnHour()
        external
        whenTheCallerIsAManager
        givenTheRoundExists
        whenValidRoundConfig
    {
        Round memory parameters =
            Round({ name: "ROUND", startTimestamp: 1, endTimestamp: 2, whitelistRoot: "", proofsUri: "" });

        Round memory round = presale.getRound(roundId);

        vm.warp(round.startTimestamp - 1 hours);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundIsLocked.selector, roundId));

        presale.updateRound(roundId, parameters);
    }

    function test_WhenRoundStartsLaterThanInAnHour()
        external
        whenTheCallerIsAManager
        givenTheRoundExists
        whenValidRoundConfig
    {
        Round memory parameters =
            Round({ name: "ROUND", startTimestamp: 1, endTimestamp: 2, whitelistRoot: "", proofsUri: "" });

        Round memory round = presale.getRound(roundId);

        vm.warp(round.startTimestamp - 1 hours - 1);

        // it emits RoundUpdated event with roundId
        vm.expectEmit(true, false, false, false);
        emit VestPresale.RoundUpdated(roundId);

        // it updates the round
        presale.updateRound(roundId, parameters);

        Round memory updated = presale.getRound(roundId);

        assertEq(parameters.name, updated.name, "updated name equals to parameters.name");
        assertEq(
            parameters.startTimestamp,
            updated.startTimestamp,
            "updated startTimestamp equals to parameters.startTimestamp"
        );
        assertEq(
            parameters.endTimestamp, updated.endTimestamp, "updated endTimestamp equals to parameters.endTimestamp"
        );
        assertEq(
            parameters.whitelistRoot, updated.whitelistRoot, "updated whitelistRoot equals to parameters.whitelistRoot"
        );
    }
}
