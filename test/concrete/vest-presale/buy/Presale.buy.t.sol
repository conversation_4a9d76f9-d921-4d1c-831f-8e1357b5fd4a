// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import { VestTest } from "test/VestTest.sol";
import { Round, RoundState } from "src/types/Round.sol";
import { Uint256Helper } from "test/utils/Uint256Helper.sol";
import { Participants } from "test/utils/Participants.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { ERC20Sample } from "test/samples/ERC20Sample.sol";
import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import { IERC721 } from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import { EmptyContract } from "test/samples/EmptyContract.sol";
import { IERC721Errors } from "@openzeppelin/contracts/interfaces/draft-IERC6093.sol";
import { ERC721RevertingReceiver } from "test/samples/ERC721RevertingReceiver.sol";
import { ERC721AcceptingReceiver } from "test/samples/ERC721AcceptingReceiver.sol";
import { ERC721ReentrantReceiver } from "test/samples/ERC721ReentrantReceiver.sol";
import { DynamicIds } from "src/libraries/DynamicIds.sol";

contract Presale_buy is VestTest {
    using Uint256Helper for *;
    using SafeERC20 for IERC20;
    using Participants for Participants.Collection;

    uint256 roundId = 1;
    Participants.Participant participant;
    uint256 membershipId;

    function setUp() public override {
        super.setUp();

        fixture(composeRounds());

        participant = participants.get(roundId, alice);
    }

    function _toAttributes(Participants.Participant memory participant_)
        private
        pure
        returns (IVestMembership.Attributes memory)
    {
        return IVestMembership.Attributes({
            price: participant_.price,
            allocation: participant_.allocation,
            claimbackPeriod: participant_.claimbackPeriod,
            tgeNumerator: participant_.tgeNumerator,
            tgeDenominator: participant_.tgeDenominator,
            cliffDuration: participant_.cliffDuration,
            cliffNumerator: participant_.cliffNumerator,
            cliffDenominator: participant_.cliffDenominator,
            vestingPeriodCount: participant_.vestingPeriodCount,
            vestingPeriodDuration: participant_.vestingPeriodDuration,
            tradeable: participant_.tradeable
        });
    }

    function test_GivenARoundIsInPendingState() external {
        assertEq(uint256(presale.getRoundState(roundId)), uint256(RoundState.PENDING), "the round is in pending state");
        vm.prank(alice);
        // it a user cannot buy
        vm.expectRevert(
            abi.encodeWithSelector(
                VestPresale.RoundStateMismatch.selector, roundId, RoundState.PENDING, RoundState.SALE
            )
        );
        presale.buy(roundId, 50 ether, _toAttributes(participant), participant.proof);
    }

    function test_GivenARoundIsInVestingState() external {
        Round memory round = presale.getRound(roundId);
        vm.warp(round.endTimestamp + 1);
        assertEq(uint256(presale.getRoundState(roundId)), uint256(RoundState.VESTING), "the round is in vesting state");
        vm.prank(alice);
        // it a user cannot buy
        vm.expectRevert(
            abi.encodeWithSelector(
                VestPresale.RoundStateMismatch.selector, roundId, RoundState.VESTING, RoundState.SALE
            )
        );
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    modifier givenARoundIsInSaleState() {
        Round memory round = presale.getRound(roundId);
        vm.warp(round.startTimestamp);
        assertEq(uint256(presale.getRoundState(roundId)), uint256(RoundState.SALE), "round 1 is in the sale state");
        _;
    }

    function test_WhenAUserManipulatesTheirPrice() external givenARoundIsInSaleState {
        IVestMembership.Attributes memory attributes = _toAttributes(participant);
        attributes.price = attributes.price / 2;
        vm.prank(alice);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, alice));
        presale.buy(roundId, participant.allocation, attributes, participant.proof);
    }

    function test_WhenAUserManipulatesTheirAllocation() external givenARoundIsInSaleState {
        IVestMembership.Attributes memory attributes = _toAttributes(participant);
        attributes.allocation = attributes.allocation * 2;
        vm.prank(alice);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, alice));
        presale.buy(roundId, participant.allocation, attributes, participant.proof);
    }

    function test_WhenAUserManipulatesTheirclaimbackPeriod() external givenARoundIsInSaleState {
        IVestMembership.Attributes memory attributes = _toAttributes(participant);
        attributes.claimbackPeriod = 1;
        vm.prank(alice);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, alice));
        presale.buy(roundId, participant.allocation, attributes, participant.proof);
    }

    function test_WhenAUserManipulatesTheirTgeNumerator() external givenARoundIsInSaleState {
        IVestMembership.Attributes memory attributes = _toAttributes(participant);
        attributes.tgeNumerator = 100;
        vm.prank(alice);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, alice));
        presale.buy(roundId, participant.allocation, attributes, participant.proof);
    }

    function test_WhenAUserManipulatesTheirTgeDenominator() external givenARoundIsInSaleState {
        IVestMembership.Attributes memory attributes = _toAttributes(participant);
        attributes.tgeDenominator = 10;
        vm.prank(alice);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, alice));
        presale.buy(roundId, participant.allocation, attributes, participant.proof);
    }

    function test_WhenTgeDenominatorEqualsZero() external givenARoundIsInSaleState {
        participants.clear(roundId);
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            0,
            0,
            0,
            0,
            10,
            1 days
        );
        participants.add(
            roundId, bob, 1 ether.decimals(decimalsB), 100 ether.decimals(decimalsA), 7 days, 10, 0, 0, 0, 0, 10, 1 days
        );
        vm.startPrank(manager);
        presale.updateWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts because division by zero is not allowed
        vm.expectRevert(Errors.DenominatorZero.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenAUserManipulatesTheirCliffDuration() external givenARoundIsInSaleState {
        participant = participants.get(roundId, carol);

        IVestMembership.Attributes memory attributes = _toAttributes(participant);

        attributes.cliffDuration = 1 hours;
        vm.prank(carol);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, carol));
        presale.buy(roundId, participant.allocation, attributes, participant.proof);
    }

    function test_WhenAUserManipulatesTheirCliffNumerator() external givenARoundIsInSaleState {
        IVestMembership.Attributes memory attributes = _toAttributes(participant);

        attributes.cliffDuration = 1;
        attributes.cliffNumerator = 10;
        vm.prank(alice);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, alice));
        presale.buy(roundId, participant.allocation, attributes, participant.proof);
    }

    function test_WhenAUserManipulatesTheirCliffDenominator() external givenARoundIsInSaleState {
        IVestMembership.Attributes memory attributes = _toAttributes(participant);

        attributes.cliffDenominator = 10;
        vm.prank(alice);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, alice));
        presale.buy(roundId, participant.allocation, attributes, participant.proof);
    }

    function test_WhenCliffDenominatorEqualsZero() external givenARoundIsInSaleState {
        participants.clear(roundId);
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            0,
            10,
            1 days
        );
        participants.add(
            roundId,
            bob,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        vm.prank(manager);
        presale.updateWhitelist(roundId, whitelistRoot, "");
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts because division by zero is not allowed
        vm.expectRevert(Errors.DenominatorZero.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenCliffNumeratorIsNonZeroAndCliffDurationIsZero() external givenARoundIsInSaleState {
        participants.clear(roundId);
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            1,
            1,
            10,
            1 days
        );
        participants.add(
            roundId,
            bob,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        vm.prank(manager);
        presale.updateWhitelist(roundId, whitelistRoot, "");
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts because tgeNumerator should be used in that case
        vm.expectRevert(VestPresale.CliffWithImmediateUnlock.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenAUserManipulatesTheirVestingPeriodCount() external givenARoundIsInSaleState {
        IVestMembership.Attributes memory attributes = _toAttributes(participant);
        attributes.vestingPeriodCount = 2;
        vm.prank(alice);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, alice));
        presale.buy(roundId, participant.allocation, attributes, participant.proof);
    }

    function test_WhenAUserManipulatesTheirVestingPeriodDuration() external givenARoundIsInSaleState {
        IVestMembership.Attributes memory attributes = _toAttributes(participant);
        attributes.vestingPeriodDuration = 1;
        vm.prank(alice);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, alice));
        presale.buy(roundId, participant.allocation, attributes, participant.proof);
    }

    function test_WhenVestingPeriodCountGreaterThanZeroAndVestingPeriodDurationIsZero()
        external
        givenARoundIsInSaleState
    {
        participants.clear(roundId);
        participants.add(
            roundId, alice, 1 ether.decimals(decimalsB), 100 ether.decimals(decimalsA), 7 days, 10, 100, 0, 0, 1, 10, 0
        );
        participants.add(
            roundId,
            bob,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        vm.prank(manager);
        presale.updateWhitelist(roundId, whitelistRoot, "");
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts
        vm.expectRevert(VestPresale.VestingWithImmediateUnlock.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenVestingPeriodCountIsOne() external givenARoundIsInSaleState {
        participants.clear(roundId);
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            1,
            1 days
        );
        participants.add(
            roundId,
            bob,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        vm.prank(manager);
        presale.updateWhitelist(roundId, whitelistRoot, "");
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts because only the cliffNumerator should be used
        vm.expectRevert(VestPresale.CliffLikeVesting.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenNoTgeNumeratorAndNoCliffDurationAndNoVestingPeriodCount() external givenARoundIsInSaleState {
        participants.clear(roundId);
        participants.add(
            roundId, alice, 1 ether.decimals(decimalsB), 100 ether.decimals(decimalsA), 7 days, 0, 1, 0, 0, 1, 0, 1 days
        );
        participants.add(
            roundId,
            bob,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        vm.prank(manager);
        presale.updateWhitelist(roundId, whitelistRoot, "");
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts
        vm.expectRevert(VestPresale.VestingWithoutUnlocks.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenVestingPeriodCountIsZeroAndCliffNumeratorGreaterThanZero() external givenARoundIsInSaleState {
        participants.clear(roundId);
        participants.add(
            roundId, alice, 1 ether.decimals(decimalsB), 100 ether.decimals(decimalsA), 7 days, 0, 1, 2 days, 1, 1, 0, 0
        );
        participants.add(
            roundId,
            bob,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        vm.prank(manager);
        presale.updateWhitelist(roundId, whitelistRoot, "");
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts because all tokens will be released at the cliff end
        vm.expectRevert(VestPresale.CliffHeightWithoutSubsequentUnlocks.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenCliffDurationIsZeroAndVestingPeriodCountIsZeroAndTgeNumeratorDoesNotEqualTgeDenominator()
        external
        givenARoundIsInSaleState
    {
        participants.clear(roundId);
        participants.add(
            roundId, alice, 1 ether.decimals(decimalsB), 100 ether.decimals(decimalsA), 7 days, 1, 2, 0, 0, 1, 0, 0
        );
        participants.add(
            roundId,
            bob,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        vm.prank(manager);
        presale.updateWhitelist(roundId, whitelistRoot, "");
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts because all tokens must be released at tge
        vm.expectRevert(VestPresale.VestingSize.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenTgeIsGreaterThan100Percent() external givenARoundIsInSaleState {
        participants.clear(roundId);
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            101,
            100,
            2 days,
            2,
            10,
            10,
            1 days
        );
        participants.add(
            roundId,
            bob,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        vm.prank(manager);
        presale.updateWhitelist(roundId, whitelistRoot, "");
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts
        vm.expectRevert(VestPresale.VestingSize.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenCliffHeightIsGreaterThan100Percent() external givenARoundIsInSaleState {
        participants.clear(roundId);
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            2 days,
            11,
            10,
            10,
            1 days
        );
        participants.add(
            roundId,
            bob,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        vm.prank(manager);
        presale.updateWhitelist(roundId, whitelistRoot, "");
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts
        vm.expectRevert(VestPresale.VestingSize.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenTheSumOfTgeAndCliffHeightIsGreaterThan100Percent() external givenARoundIsInSaleState {
        participants.clear(roundId);
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            6,
            11,
            2 days,
            5,
            10,
            10,
            1 days
        );
        participants.add(
            roundId,
            bob,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            1,
            10,
            1 days
        );
        bytes32 whitelistRoot = participants.getWhitelistRoot(roundId);
        vm.prank(manager);
        presale.updateWhitelist(roundId, whitelistRoot, "");
        participant = participants.get(roundId, alice);
        vm.prank(alice);
        // it reverts
        vm.expectRevert(VestPresale.VestingSize.selector);
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenAnAttackerUsesAliceAttributesAndProofs() external givenARoundIsInSaleState {
        vm.startPrank(chuck);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, chuck));
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenAUserPurchasesZeroTokens() external givenARoundIsInSaleState {
        vm.startPrank(alice);
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.buy(roundId, 0, _toAttributes(participant), participant.proof);
    }

    function test_WhenAUserPurchasesFromANonExistentRound() external givenARoundIsInSaleState {
        vm.startPrank(alice);
        uint256 nonExistentRoundId = 4;
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundNotExists.selector, nonExistentRoundId));
        presale.buy(nonExistentRoundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenAUserPurchasesMoreTokensThanTheirAllocation() external givenARoundIsInSaleState {
        vm.startPrank(alice);
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.buy(roundId, participant.allocation + 1, _toAttributes(participant), participant.proof);
    }

    function test_GivenTokenBHasTransferFees() external givenARoundIsInSaleState {
        tokenB = new ERC20Sample(uint8(decimalsB), 2);
        fixture(composeRounds());
        participant = participants.get(roundId, alice);
        Round memory round = presale.getRound(roundId);
        vm.warp(round.startTimestamp);
        assertEq(uint256(presale.getRoundState(roundId)), uint256(RoundState.SALE), "round 1 is in the sale state");
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.TokenWithTransferFees.selector, tokenB));
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenTheCallerIsAUserWithZeroPrice() external givenARoundIsInSaleState {
        participant = participants.get(roundId, bob);
        vm.startPrank(bob);
        membershipId = presale.buy(roundId, participant.allocation / 2, _toAttributes(participant), participant.proof);
        // it receives Membership without the need for an allowance
        assertEq(membership.balanceOf(bob), 1, "bob owns one Membership");
        // it their Membership reflects how much they bought and how much they can buy
        assertEq(
            roundId, membership.getRoundId(membershipId), "bob owns Membership assigned to the round he participated in"
        );
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        assertEq(usage.max, participant.allocation / 2, "bob bought half of his allocation");
    }

    function test_GivenTheUserDidNotGiveAnAllowanceForTokenB() external givenARoundIsInSaleState {
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        uint256 allowanceB = amountB - 1;
        tokenB.forceApprove(address(presale), allowanceB);
        // it reverts
        vm.expectRevert(); // error depends on token implementation (USDT on ethereum - Address.FailedInnerCall, default - IERC20Errors.ERC20InsufficientAllowance)
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_GivenTheUserDoesNotHaveEnoughTokenB() external givenARoundIsInSaleState {
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;
        uint256 balanceB = amountB - 1;
        tokenB.safeTransfer(alice, balanceB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        // it reverts
        vm.expectRevert(); // error depends on token implementation (USDT on ethereum - Address.FailedInnerCall, default - IERC20Errors.ERC20InsufficientBalance)
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenTheUserCallsBuyMethodAfterASuccessfulFirstBuy() external givenARoundIsInSaleState {
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        presale.buy(roundId, participant.allocation / 2, _toAttributes(participant), participant.proof);
        // it reverts because proofs are single use
        vm.expectRevert(abi.encodeWithSelector(VestPresale.AlreadyRoundParticipant.selector, roundId, alice));
        presale.buy(roundId, participant.allocation / 2, _toAttributes(participant), participant.proof);
    }

    function test_WhenTheUserBuysLessThanTheirAllocation() external givenARoundIsInSaleState {
        uint256 amountA = participant.allocation / 2;
        uint256 amountB = amountA * participant.price / 10 ** decimalsA;
        uint256 liquidityA = presale.liquidityA();
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        uint256 publicId = DynamicIds.createPublicId(
            DynamicIds.createMintId(abi.encodePacked(alice, roundId, amountA, block.timestamp)),
            abi.encode(IVestMembership.Usage({ max: amountA, current: 0 }))
        );
        vm.expectEmit(true, true, true, false);
        emit IERC721.Transfer(address(0), alice, publicId);
        membershipId = presale.buy(roundId, amountA, _toAttributes(participant), participant.proof);
        // it pays with tokenB
        assertEq(tokenB.balanceOf(alice), 0, "alice has spent her B tokens");
        assertEq(tokenB.balanceOf(address(presale)), amountB, "presale sc owns token B");
        // it mints Membership with correct attributes and usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        assertEq(usage.current, 0, "usage.current equals the amount bought");
        assertEq(usage.max, amountA, "usage.max equals participant allocation");
        IVestMembership.Attributes memory attributes = membership.getAttributes(membershipId);
        assertEq(attributes.allocation, participant.allocation, "Membership allocation equals participant allocation");
        assertEq(attributes.price, participant.price, "Membership price equals participant price");
        assertEq(
            attributes.cliffDuration,
            participant.cliffDuration,
            "Membership cliffDuration equals participant cliffDuration"
        );
        assertEq(
            attributes.cliffNumerator,
            participant.cliffNumerator,
            "Membership cliffNumerator equals participant cliffNumerator"
        );
        assertEq(
            attributes.cliffDenominator,
            participant.cliffDenominator,
            "Membership cliffDenominator equals participant cliffDenominator"
        );
        assertEq(
            attributes.claimbackPeriod,
            participant.claimbackPeriod,
            "Membership claimbackPeriod equals participant claimbackPeriod"
        );
        assertEq(
            attributes.tgeNumerator, participant.tgeNumerator, "Membership tgeNumerator equals participant tgeNumerator"
        );
        assertEq(
            attributes.tgeDenominator,
            participant.tgeDenominator,
            "Membership tgeDenominator equals participant tgeDenominator"
        );
        assertEq(
            attributes.vestingPeriodCount,
            participant.vestingPeriodCount,
            "Membership vestingPeriodCount equals participant vestingPeriodCount"
        );
        assertEq(
            attributes.vestingPeriodDuration,
            participant.vestingPeriodDuration,
            "Membership vestingPeriodDuration equals participant vestingPeriodDuration"
        );
        // it the caller owns the Membership
        assertEq(membership.ownerOf(membershipId), alice, "alice owns the membership");
        // liquidityA decreases
        assertEq(presale.liquidityA(), liquidityA - amountA, "liquidityA decreases");
        // liquidityB increases
        assertEq(presale.liquidityB(), amountB, "liquidityB increases");
        // it user does not receive any of the tokenA
        assertEq(tokenA.balanceOf(alice), 0, "alice has zero tokenA");
    }

    function test_WhenTheUserBuysTheirEntireAllocation() external givenARoundIsInSaleState {
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;
        uint256 liquidityA = presale.liquidityA();
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        membershipId = DynamicIds.createPublicId(
            DynamicIds.createMintId(abi.encodePacked(alice, roundId, participant.allocation, block.timestamp)),
            abi.encodePacked(participant.allocation, participant.allocation, false)
        );
        vm.expectEmit(false, false, false, true);
        emit IERC721.Transfer(address(0), alice, membershipId);
        membershipId = presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
        // it pays with tokenB
        assertEq(tokenB.balanceOf(alice), 0, "alice has spent her B tokens");
        assertEq(tokenB.balanceOf(address(presale)), amountB, "presale sc owns token B");
        // it mints Membership with correct attributes and usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        assertEq(usage.current, 0, "usage.current equals the amount claimed");
        assertEq(usage.max, participant.allocation, "usage.max equals participant allocation");
        IVestMembership.Attributes memory attributes = membership.getAttributes(membershipId);
        assertEq(attributes.allocation, participant.allocation, "Membership allocation equals participant allocation");
        assertEq(attributes.price, participant.price, "Membership price equals participant price");
        assertEq(
            attributes.cliffDuration,
            participant.cliffDuration,
            "Membership cliffDuration equals participant cliffDuration"
        );
        assertEq(
            attributes.cliffNumerator,
            participant.cliffNumerator,
            "Membership cliffNumerator equals participant cliffNumerator"
        );
        assertEq(
            attributes.cliffDenominator,
            participant.cliffDenominator,
            "Membership cliffDenominator equals participant cliffDenominator"
        );
        assertEq(
            attributes.claimbackPeriod,
            participant.claimbackPeriod,
            "Membership claimbackPeriod equals participant claimbackPeriod"
        );
        assertEq(
            attributes.tgeNumerator, participant.tgeNumerator, "Membership tgeNumerator equals participant tgeNumerator"
        );
        assertEq(
            attributes.tgeDenominator,
            participant.tgeDenominator,
            "Membership tgeDenominator equals participant tgeDenominator"
        );
        assertEq(
            attributes.vestingPeriodCount,
            participant.vestingPeriodCount,
            "Membership vestingPeriodCount equals participant vestingPeriodCount"
        );
        assertEq(
            attributes.vestingPeriodDuration,
            participant.vestingPeriodDuration,
            "Membership vestingPeriodDuration equals participant vestingPeriodDuration"
        );
        // it the caller owns the Membership
        assertEq(membership.ownerOf(membershipId), alice, "alice owns the membership");
        // liquidityA decreases
        assertEq(presale.liquidityA(), liquidityA - participant.allocation, "liquidityA decreases");
        // liquidityB increases
        assertEq(presale.liquidityB(), amountB, "liquidityB increases");
    }

    modifier givenTokenALiquidityIsLowerThanUsersAllocation() {
        uint256 liquidityA = presale.liquidityA();
        uint256 excessiveLiquidityA = liquidityA - participant.allocation;
        vm.prank(beneficiary);
        presale.withdrawTokenA(excessiveLiquidityA + 1);
        assertLt(presale.liquidityA(), participant.allocation, unicode"liquidity is lower than user’s allocation");
        _;
    }

    function test_WhenTheUserBuysLessOrEqualThanAvailableLiquidity()
        external
        givenARoundIsInSaleState
        givenTokenALiquidityIsLowerThanUsersAllocation
    {
        uint256 liquidityA = presale.liquidityA();
        uint256 amountB = liquidityA * participant.price / 10 ** decimalsA;
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        membershipId = DynamicIds.createPublicId(
            DynamicIds.createMintId(abi.encodePacked(alice, roundId, liquidityA, block.timestamp)),
            abi.encode(IVestMembership.Usage({ current: 0, max: liquidityA }))
        );
        vm.expectEmit(true, true, false, true);
        emit IERC20.Transfer(alice, address(presale), amountB);
        vm.expectEmit(true, true, true, false);
        emit IERC721.Transfer(address(0), alice, membershipId);
        membershipId = presale.buy(roundId, liquidityA, _toAttributes(participant), participant.proof);
        // it pays with tokenB
        assertEq(tokenB.balanceOf(alice), 0, "alice has spent her B tokens");
        assertEq(tokenB.balanceOf(address(presale)), amountB, "presale sc owns token B");
        // it mints Membership with correct attributes and usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        assertEq(usage.current, 0, "usage.current equals zero");
        assertEq(usage.max, liquidityA, "usage.max equals amount bought");
        IVestMembership.Attributes memory attributes = membership.getAttributes(membershipId);
        assertEq(attributes.allocation, participant.allocation, "Membership allocation equals participant allocation");
        assertEq(attributes.price, participant.price, "Membership price equals participant price");
        assertEq(
            attributes.cliffDuration,
            participant.cliffDuration,
            "Membership cliffDuration equals participant cliffDuration"
        );
        assertEq(
            attributes.cliffNumerator,
            participant.cliffNumerator,
            "Membership cliffNumerator equals participant cliffNumerator"
        );
        assertEq(
            attributes.cliffDenominator,
            participant.cliffDenominator,
            "Membership cliffDenominator equals participant cliffDenominator"
        );
        assertEq(
            attributes.claimbackPeriod,
            participant.claimbackPeriod,
            "Membership claimbackPeriod equals participant claimbackPeriod"
        );
        assertEq(
            attributes.tgeNumerator, participant.tgeNumerator, "Membership tgeNumerator equals participant tgeNumerator"
        );
        assertEq(
            attributes.tgeDenominator,
            participant.tgeDenominator,
            "Membership tgeDenominator equals participant tgeDenominator"
        );
        assertEq(
            attributes.vestingPeriodCount,
            participant.vestingPeriodCount,
            "Membership vestingPeriodCount equals participant vestingPeriodCount"
        );
        assertEq(
            attributes.vestingPeriodDuration,
            participant.vestingPeriodDuration,
            "Membership vestingPeriodDuration equals participant vestingPeriodDuration"
        );
        // it the caller owns the Membership
        assertEq(membership.ownerOf(membershipId), alice, "alice owns the membership");
        // liquidityA decreases
        assertEq(presale.liquidityA(), 0, "liquidityA decreases");
        // liquidityB increases
        assertEq(presale.liquidityB(), amountB, "liquidityB increases");
    }

    function test_WhenTheUserBuysMoreThanAvailableLiquidity()
        external
        givenARoundIsInSaleState
        givenTokenALiquidityIsLowerThanUsersAllocation
    {
        uint256 liquidityA = presale.liquidityA();
        uint256 amountB = liquidityA * participant.price / 10 ** decimalsA;
        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);
        uint256 publicId = DynamicIds.createPublicId(
            DynamicIds.createMintId(abi.encodePacked(alice, roundId, participant.allocation - 1, block.timestamp)),
            abi.encode(IVestMembership.Usage({ max: participant.allocation - 1, current: 0 }))
        );
        vm.expectEmit(true, true, true, false);
        emit IERC721.Transfer(address(0), alice, publicId);
        membershipId = presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
        // it pays with tokenB
        assertEq(tokenB.balanceOf(alice), 0, "alice has spent her B tokens");
        assertEq(tokenB.balanceOf(address(presale)), amountB, "presale sc owns token B");
        // it mints Membership with correct attributes and usage
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        assertEq(usage.current, 0, "usage.current equals the claimed amount");
        assertEq(usage.max, participant.allocation - 1, "usage.max equals participant allocation");
        IVestMembership.Attributes memory attributes = membership.getAttributes(membershipId);
        assertEq(attributes.allocation, participant.allocation, "Membership allocation equals participant allocation");
        assertEq(attributes.price, participant.price, "Membership price equals participant price");
        assertEq(
            attributes.cliffDuration,
            participant.cliffDuration,
            "Membership cliffDuration equals participant cliffDuration"
        );
        assertEq(
            attributes.cliffNumerator,
            participant.cliffNumerator,
            "Membership cliffNumerator equals participant cliffNumerator"
        );
        assertEq(
            attributes.cliffDenominator,
            participant.cliffDenominator,
            "Membership cliffDenominator equals participant cliffDenominator"
        );
        assertEq(
            attributes.claimbackPeriod,
            participant.claimbackPeriod,
            "Membership claimbackPeriod equals participant claimbackPeriod"
        );
        assertEq(
            attributes.tgeNumerator, participant.tgeNumerator, "Membership tgeNumerator equals participant tgeNumerator"
        );
        assertEq(
            attributes.tgeDenominator,
            participant.tgeDenominator,
            "Membership tgeDenominator equals participant tgeDenominator"
        );
        assertEq(
            attributes.vestingPeriodCount,
            participant.vestingPeriodCount,
            "Membership vestingPeriodCount equals participant vestingPeriodCount"
        );
        assertEq(
            attributes.vestingPeriodDuration,
            participant.vestingPeriodDuration,
            "Membership vestingPeriodDuration equals participant vestingPeriodDuration"
        );
        // it the caller owns the Membership
        assertEq(membership.ownerOf(membershipId), alice, "alice owns the membership");
        // liquidityA decreases
        assertEq(presale.liquidityA(), 0, "liquidityA decreases");
        // liquidityB increases
        assertEq(presale.liquidityB(), amountB, "liquidityB increases");
    }

    modifier givenAUserIsASmartContract() {
        _;
    }

    function test_GivenAUserDoesNotImplementTheHook() external givenARoundIsInSaleState givenAUserIsASmartContract {
        address smartContract = address(new EmptyContract());
        participants.clear(roundId);
        participants.add(
            roundId,
            smartContract,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            100,
            10,
            1 days
        );
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            0,
            0,
            0,
            0,
            10,
            1 days
        );
        vm.startPrank(manager);
        presale.updateWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();
        participant = participants.get(roundId, smartContract);
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;
        tokenB.safeTransfer(smartContract, amountB);
        vm.startPrank(smartContract);
        tokenB.forceApprove(address(presale), amountB);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721InvalidReceiver.selector, smartContract));
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    modifier givenAUserDoesImplementTheHook() {
        _;
    }

    function test_WhenAUserReverts()
        external
        givenARoundIsInSaleState
        givenAUserIsASmartContract
        givenAUserDoesImplementTheHook
    {
        address smartContract = address(new ERC721RevertingReceiver());
        participants.clear(roundId);
        participants.add(
            roundId,
            smartContract,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            100,
            10,
            1 days
        );
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            0,
            0,
            0,
            0,
            10,
            1 days
        );
        vm.startPrank(manager);
        presale.updateWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();
        participant = participants.get(roundId, smartContract);
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;
        tokenB.safeTransfer(smartContract, amountB);
        vm.startPrank(smartContract);
        tokenB.forceApprove(address(presale), amountB);
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721InvalidReceiver.selector, smartContract));
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    modifier whenAUserDoesNotRevert() {
        _;
    }

    function test_WhenThereIsAReentrancy()
        external
        givenARoundIsInSaleState
        givenAUserIsASmartContract
        givenAUserDoesImplementTheHook
        whenAUserDoesNotRevert
    {
        ERC721ReentrantReceiver eRC721ReentrantReceiver = new ERC721ReentrantReceiver();

        participants.clear(roundId);
        participants.add(
            roundId,
            address(eRC721ReentrantReceiver),
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            100,
            10,
            1 days
        );
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            0,
            0,
            0,
            0,
            10,
            1 days
        );
        vm.startPrank(manager);
        presale.updateWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();

        eRC721ReentrantReceiver.hydrate(
            address(presale),
            abi.encodeWithSignature(
                "buy(uint256,uint256,(uint256,uint256,uint256,uint32,uint32,uint32,uint32,uint32,uint32,uint32,uint8),bytes32[])",
                roundId,
                participant.allocation,
                _toAttributes(participant),
                participant.proof
            )
        );

        participant = participants.get(roundId, address(eRC721ReentrantReceiver));

        uint256 amountB = (participant.allocation * participant.price / 10 ** decimalsA) * 2;
        tokenB.safeTransfer(address(eRC721ReentrantReceiver), amountB);

        vm.startPrank(address(eRC721ReentrantReceiver));
        tokenB.forceApprove(address(presale), amountB);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, address(eRC721ReentrantReceiver)));
        presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
    }

    function test_WhenThereIsNoReentrancy()
        external
        givenARoundIsInSaleState
        givenAUserIsASmartContract
        givenAUserDoesImplementTheHook
        whenAUserDoesNotRevert
    {
        address smartContract = address(new ERC721AcceptingReceiver());
        participants.clear(roundId);
        participants.add(
            roundId,
            smartContract,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            100,
            10,
            1 days
        );
        participants.add(
            roundId,
            alice,
            1 ether.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            0,
            0,
            0,
            0,
            10,
            1 days
        );
        vm.startPrank(manager);
        presale.updateWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();
        participant = participants.get(roundId, smartContract);
        uint256 amountB = participant.allocation * participant.price / 10 ** decimalsA;
        tokenB.safeTransfer(smartContract, amountB);
        vm.startPrank(smartContract);
        tokenB.forceApprove(address(presale), amountB);
        membershipId = presale.buy(roundId, participant.allocation, _toAttributes(participant), participant.proof);
        // it pays with tokenB
        assertEq(tokenB.balanceOf(smartContract), 0, "smart contract spent tokenB");
        assertEq(tokenB.balanceOf(address(presale)), presale.liquidityB(), "presale liquidity increased");
        // it the caller owns the Membership
        assertEq(membership.ownerOf(membershipId), smartContract, "smart contract owns the Membership");
    }

    function test_GivenAUserHasMultipleAllocations() external givenARoundIsInSaleState {
        participants.add(
            roundId,
            alice,
            0.1e18.decimals(decimalsB),
            200 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            100,
            10,
            1 days
        );

        vm.startPrank(manager);
        presale.updateWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();

        Participants.Participant memory participant0 = participants.get(roundId, alice, 0);
        Participants.Participant memory participant1 = participants.get(roundId, alice, 1);

        uint256 amountB0 = participant0.allocation * participant0.price / 10 ** decimalsA;
        uint256 amountB1 = participant1.allocation * participant1.price / 10 ** decimalsA;

        tokenB.safeTransfer(alice, amountB0 + amountB1);

        vm.startPrank(alice);

        tokenB.forceApprove(address(presale), amountB0 + amountB1);

        // it mints Membership for the first allocation
        uint256 membershipId0 =
            presale.buy(roundId, participant0.allocation, _toAttributes(participant0), participant0.proof);

        IVestMembership.Usage memory usage0 = membership.getUsage(membershipId0);
        assertEq(usage0.current, 0, "usage0.current equals the amount bought");
        assertEq(usage0.max, participant0.allocation, "usage0.max equals participant0 allocation");

        // it mints Membership for the second allocation
        uint256 membershipId1 =
            presale.buy(roundId, participant1.allocation, _toAttributes(participant1), participant1.proof);

        IVestMembership.Usage memory usage1 = membership.getUsage(membershipId1);
        assertEq(usage1.current, 0, "usage1.current equals the amount bought");
        assertEq(usage1.max, participant1.allocation, "usage1.max equals participant1 allocation");
    }
}
