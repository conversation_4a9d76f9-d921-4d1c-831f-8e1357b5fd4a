// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import { VestPresale } from "src/VestPresale.sol";

import { VestTest } from "test/VestTest.sol";
import { ERC20Sample } from "test/samples/ERC20Sample.sol";
import { Uint256Helper } from "test/utils/Uint256Helper.sol";

contract Presale_depositTokenA is VestTest {
    using Uint256Helper for *;
    using SafeERC20 for IERC20;

    function setUp() public override {
        super.setUp();

        fixture();
    }

    modifier whenTheSenderIsAnyone() {
        _;
    }

    function test_GivenTokenAHasTransferFees() external {
        tokenA = new ERC20Sample(decimalsA, 2);

        fixture();

        tokenA.transfer(alice, 1 ether.decimals(decimalsA));

        vm.startPrank(alice);

        tokenA.approve(address(presale), 1 ether.decimals(decimalsA));

        uint256 amount = tokenA.balanceOf(alice);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.TokenWithTransferFees.selector, tokenA));

        presale.depositTokenA(amount);
    }

    modifier givenTokenAHasNoTransferFees() {
        _;
    }

    function test_GivenTokenAFeesEqualZero(uint256 amount)
        external
        whenTheSenderIsAnyone
        givenTokenAHasNoTransferFees
    {
        tokenA.safeTransfer(alice, 1 ether.decimals(decimalsA));

        vm.startPrank(alice);

        tokenA.forceApprove(address(presale), 1 ether.decimals(decimalsA));

        vm.assume(amount <= tokenA.balanceOf(alice));

        vm.expectEmit(false, false, false, true);
        emit VestPresale.DepositedA(amount);

        uint256 balance = presale.liquidityA();

        presale.depositTokenA(amount);

        assertEq(balance + amount, presale.liquidityA(), "liquidityA increases by the given amount");
    }
}
