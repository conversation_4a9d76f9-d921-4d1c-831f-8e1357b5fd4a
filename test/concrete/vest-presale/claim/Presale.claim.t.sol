// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC721Errors } from "@openzeppelin/contracts/interfaces/draft-IERC6093.sol";

import { IVestMembership } from "src/IVestMembership.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { DynamicIds } from "src/libraries/DynamicIds.sol";

import { Uint256Helper } from "test/utils/Uint256Helper.sol";
import { Participants } from "test/utils/Participants.sol";
import { VestTest, Configuration } from "test/VestTest.sol";
import { EmptyContract } from "test/samples/EmptyContract.sol";
import { ERC721RevertingReceiver } from "test/samples/ERC721RevertingReceiver.sol";
import { ERC721AcceptingReceiver } from "test/samples/ERC721AcceptingReceiver.sol";
import { ERC721ReentrantReceiver } from "test/samples/ERC721ReentrantReceiver.sol";

contract Presale_claim is VestTest {
    using Uint256Helper for *;
    using Participants for Participants.Collection;

    uint256 internal roundId = 1;
    uint256 internal membershipId;

    address internal sender;

    Participants.Participant internal participant = Participants.Participant({
        account: alice,
        price: 0.1e18.decimals(decimalsB),
        allocation: 100 ether.decimals(decimalsA),
        claimbackPeriod: 7 days,
        tgeNumerator: 10,
        tgeDenominator: 100,
        cliffDuration: 1 days,
        cliffNumerator: 0,
        cliffDenominator: 100,
        vestingPeriodCount: 10,
        vestingPeriodDuration: 1 days,
        tradeable: 2,
        proof: new bytes32[](0)
    });

    function setUp() public override {
        super.setUp();

        Configuration memory configuration = composeConfiguration();

        configuration.presale.tgeTimestamp = 0;
        configuration.presale.listingTimestamp = 0;

        fixture(configuration);
    }

    function test_WhenACallerDoesNotOwnTheMembership() external {
        membershipId = fixture_buy(1, alice);

        vm.startPrank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, chuck));
        presale.claim(membershipId);
    }

    function _impersonate(Participants.Participant memory participant_) internal {
        participants.replace(
            roundId,
            participant_.account,
            participant_.price,
            participant_.allocation,
            participant_.claimbackPeriod,
            participant_.tgeNumerator,
            participant_.tgeDenominator,
            participant_.cliffDuration,
            participant_.cliffNumerator,
            participant_.cliffDenominator,
            participant_.vestingPeriodCount,
            participant_.vestingPeriodDuration
        );

        vm.startPrank(manager);
        presale.updateWhitelist(roundId, participants.getWhitelistRoot(roundId), "");
        vm.stopPrank();

        uint256 timestamp = block.timestamp;

        membershipId = fixture_buy(roundId, sender = participant_.account);

        vm.warp(timestamp);
        vm.startPrank(sender);
    }

    modifier asMember() {
        _impersonate(participant);
        _;
    }

    modifier whenACallerOwnsTheMembership() {
        _;
    }

    function test_GivenTgeTimestampEqualsZero() external whenACallerOwnsTheMembership asMember {
        // it reverts because users must retain an ability to claimback until the tgeTimestamp is known
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);
    }

    modifier givenTgeTimestampIsGreaterThanZero() {
        vm.prank(beneficiary);
        presale.updateTgeTimestamp(vm.unixTime() * 2);
        _;
    }

    modifier givenMembershipUsageMaxAndUsageCurrentGreaterThanZero() {
        _;
    }

    function test_GivenBlockTimestampIsLowerThanTgeTimestamp()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        asMember
    {
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));

        // it reverts because users must retain an ability to claimback in case tgeTimestamp is moved into the future
        presale.claim(membershipId);
    }

    modifier givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp() {
        vm.warp(presale.getTgeTimestamp() + 1);
        _;
    }

    function test_GivenMembershipUsageMaxOrUsageCurrentEqualsZero()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        asMember
    {
        membershipId = presale.claim(membershipId);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));

        // it reverts because there is nothing to claim
        presale.claim(membershipId);
    }

    function test_GivenTheCallerIsNotASmartContract()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        asMember
    {
        IVestMembership.Usage memory usageBefore = membership.getUsage(membershipId);
        IVestMembership.Attributes memory attributes = membership.getAttributes(membershipId);
        uint256 releasableAtTge = participant.allocation * participant.tgeNumerator / participant.tgeDenominator;
        assertEq(presale.nonClaimableBackTokenB(), 0, "all tokenB is claimableBack");
        assertEq(tokenA.balanceOf(sender), 0, "sender owns no tokenA");
        // it emits Claimed event with the Membership id
        vm.expectEmit(true, false, false, true);
        emit VestPresale.Claimed(membershipId, releasableAtTge);
        membershipId = presale.claim(membershipId);
        IVestMembership.Usage memory usageAfter = membership.getUsage(membershipId);

        // it increases nonClaimableBackTokenB based on Membership usage current and attributes price
        assertEq(
            presale.nonClaimableBackTokenB(),
            usageBefore.max * attributes.price / 10 ** decimalsA,
            "all tokenB collected with that Membership has be announced as non-claimableBack"
        );
        // it decreases Membership usage max by usage current
        assertEq(
            usageAfter.max, usageBefore.max - usageBefore.current, "decreases Membership usage.max by usage.current"
        );
        // it mints a new Membership to the caller
        assertEq(membership.balanceOf(sender), 1, "sender owns Membership");
        // a new Membership represents a title to the right amount of tokens
        assertEq(
            membership.getUsage(membershipId).max,
            participant.allocation,
            "Membership represents a title to the right amount of tokens"
        );
        // it releases tokenA
        assertGt(tokenA.balanceOf(sender), 0, "sender owns some tokenA");
        // it keeps track of Membership usage according to the amount of released tokenA
        assertEq(tokenA.balanceOf(sender), membership.getUsage(membershipId).current, "Membership usage is updated");
    }

    modifier givenTheCallerIsASmartContract() {
        _;
    }

    function test_GivenTheyDoNotImplementTheHook()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        asMember
        givenTheCallerIsASmartContract
    {
        vm.warp(presale.getTgeTimestamp());

        address empty = address(new EmptyContract());

        membership.transferFrom(sender, empty, membershipId);

        vm.startPrank(empty);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721InvalidReceiver.selector, empty));
        presale.claim(membershipId);
    }

    modifier givenTheyImplementTheHook() {
        _;
    }

    function test_WhenTheyRevert()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        asMember
        givenTheCallerIsASmartContract
        givenTheyImplementTheHook
    {
        vm.warp(presale.getTgeTimestamp());

        address eRC721RevertingReceiver = address(new ERC721RevertingReceiver());

        membership.transferFrom(sender, eRC721RevertingReceiver, membershipId);

        vm.startPrank(eRC721RevertingReceiver);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(IERC721Errors.ERC721InvalidReceiver.selector, eRC721RevertingReceiver));
        presale.claim(membershipId);
    }

    modifier whenTheyDoNotRevert() {
        _;
    }

    function test_WhenThereIsAReentrancy()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        asMember
        givenTheCallerIsASmartContract
        givenTheyImplementTheHook
        whenTheyDoNotRevert
    {
        vm.warp(presale.getTgeTimestamp());

        ERC721ReentrantReceiver eRC721ReentrantReceiver = new ERC721ReentrantReceiver();

        membership.transferFrom(sender, address(eRC721ReentrantReceiver), membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        uint256 nextId = DynamicIds.createPublicId(
            membershipId,
            abi.encode(IVestMembership.Usage({ current: membership.unlocked(membershipId), max: usage.max }))
        );

        eRC721ReentrantReceiver.hydrate(address(presale), abi.encodeWithSignature("claim(uint256)", nextId));

        vm.startPrank(address(eRC721ReentrantReceiver));

        // it reverts because Membership was already marked as non claimableBack
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, nextId));
        presale.claim(membershipId);
    }

    function test_WhenThereIsNoReentrancy()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        asMember
        givenTheCallerIsASmartContract
        givenTheyImplementTheHook
        whenTheyDoNotRevert
    {
        vm.warp(presale.getTgeTimestamp());

        uint256 nonClaimableBackTokenB = presale.nonClaimableBackTokenB();
        address eRC721AcceptingReceiver = address(new ERC721AcceptingReceiver());
        uint256 releasableAtTge = participant.allocation * participant.tgeNumerator / participant.tgeDenominator;

        membership.transferFrom(sender, eRC721AcceptingReceiver, membershipId);
        vm.startPrank(eRC721AcceptingReceiver);

        // it emits Claimed event with membership id and amountA
        vm.expectEmit(true, false, false, true);
        emit VestPresale.Claimed(membershipId, releasableAtTge);

        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        // it mints a new Membership to the caller
        assertEq(usage.max, participant.allocation, "membership usage.max equals to participant.allocation");
        assertEq(usage.current, releasableAtTge, "membership usage.max equals to tge");

        assertEq(
            tokenA.balanceOf(address(eRC721AcceptingReceiver)), releasableAtTge, "sender tokenA balance equals to tge"
        );

        // it increases nonClaimableBackTokenB based on Membership usage current and attributes price
        assertEq(
            presale.nonClaimableBackTokenB(),
            nonClaimableBackTokenB + participant.allocation * participant.price / 10 ** decimalsA,
            "increases nonClaimableBackTokenB based on Membership usage current and attributes price"
        );
    }

    function test_GivenCurrentUsageEqualsMax()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        asMember
    {
        uint256 vestingEndsAt = presale.getTgeTimestamp() + participant.cliffDuration
            + participant.vestingPeriodDuration * participant.vestingPeriodCount;
        vm.warp(vestingEndsAt);

        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals to participant.allocation");
        assertEq(usage.current, participant.allocation, "usage.participant equals to participant.allocation");

        assertEq(tokenA.balanceOf(address(sender)), participant.allocation);

        // it unlocks nothing
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);
    }

    function test_GivenBlockTimestampAfterVestingEnds()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        asMember
    {
        // it unlocks usage max minus usage current
        membershipId = presale.claim(membershipId);

        skip(participant.vestingPeriodDuration * participant.vestingPeriodCount);

        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals to participant.allocation");
        assertEq(usage.current, participant.allocation, "usage.participant equals to participant.allocation");

        assertEq(tokenA.balanceOf(address(sender)), participant.allocation);
    }

    modifier givenCurrentUsageLowerThanMax() {
        _;
    }

    modifier givenTgeNumeratorEqualsZero() {
        participant.tgeNumerator = 0;
        _;
    }

    modifier givenNoTgeAndNoCliff() {
        participant.tgeNumerator = participant.cliffDuration = 0;
        _;
    }

    function test_GivenVestingPeriodCountMoreThanOne()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        givenCurrentUsageLowerThanMax
        givenTgeNumeratorEqualsZero
        givenNoTgeAndNoCliff
        asMember
    {
        vm.warp(presale.getTgeTimestamp());

        // it unlocks nothing at tgeTimestamp
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks nothing during the first period
        skip(participant.vestingPeriodDuration / 2);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks at the end of the first period
        // it emits Claimed event with Membership id and amountA
        skip(participant.vestingPeriodDuration / 2);

        uint256 amount = participant.allocation / participant.vestingPeriodCount;

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), amount);

        // it unlocks nothing during the period
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks all at the end of the last period
        skip(participant.vestingPeriodDuration * participant.vestingPeriodCount);

        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        // it increases Membership usage
        assertEq(usage.max, participant.allocation, "usage.max equals to participant.allocation");
        assertEq(usage.current, participant.allocation, "usage.participant equals to participant.allocation");

        assertEq(tokenA.balanceOf(address(sender)), participant.allocation);

        // it unlocks nothing after that
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);
    }

    modifier givenNoTgeAndCliffDuration() {
        participant.tgeNumerator = 0;
        participant.cliffDuration = 1 days;
        _;
    }

    modifier givenNoTgeAndCliffNumeratorIsZero() {
        participant.tgeNumerator = 0;
        participant.cliffNumerator = 0;
        _;
    }

    function test_GivenFlatCliffAndVestingPeriodCountGreaterThanOne()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        givenCurrentUsageLowerThanMax
        givenTgeNumeratorEqualsZero
        givenNoTgeAndCliffDuration
        givenNoTgeAndCliffNumeratorIsZero
        asMember
    {
        // it unlocks nothing at tgeTimestamp
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks nothing during the cliff
        skip(participant.cliffDuration / 2);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        uint256 amount = participant.allocation / participant.vestingPeriodCount;

        // it unlocks first period amount at cliff end
        skip(participant.cliffDuration / 2);

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), amount);

        // it unlocks nothing during the period
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks at the end of the first period
        // it emits Claimed event with Membership id and amountA
        skip(participant.vestingPeriodDuration - 1);

        vm.expectEmit(true, false, false, true);
        emit VestPresale.Claimed(membershipId, amount);

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), amount * 2);

        // it unlocks nothing during the period
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks all at the end of the last period
        uint256 vestingEndsAt = presale.getTgeTimestamp() + participant.cliffDuration
            + participant.vestingPeriodDuration * participant.vestingPeriodCount;
        vm.warp(vestingEndsAt);

        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        // it increases Membership usage
        assertEq(usage.max, participant.allocation, "usage.max equals to participant.allocation");
        assertEq(usage.current, participant.allocation, "usage.participant equals to participant.allocation");

        assertEq(tokenA.balanceOf(address(sender)), participant.allocation);

        // it unlocks nothing after that
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);
    }

    modifier givenCliffNumeratorIsNotZero() {
        participant.cliffNumerator = 10;
        _;
    }

    function test_GivenCliffAndVestingPeriodCountGreaterThanOne()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        givenCurrentUsageLowerThanMax
        givenTgeNumeratorEqualsZero
        givenNoTgeAndCliffDuration
        givenCliffNumeratorIsNotZero
        asMember
    {
        // it unlocks nothing at tgeTimestamp
        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks nothing during the cliff
        skip(participant.cliffDuration / 2);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks cliff amount on cliff end
        skip(participant.cliffDuration / 2);

        uint256 cliffUnlock = (participant.allocation * participant.cliffNumerator) / participant.cliffDenominator;

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), cliffUnlock, "it unlocks cliff amount on cliff end");

        // it unlocks nothing during the first period
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks at the end of the first period
        skip(participant.vestingPeriodDuration - 1);

        uint256 installment = (participant.allocation - cliffUnlock) / participant.vestingPeriodCount;

        vm.expectEmit(true, false, false, true);
        emit VestPresale.Claimed(membershipId, installment);

        membershipId = presale.claim(membershipId);

        assertEq(
            tokenA.balanceOf(address(sender)), cliffUnlock + installment, "it unlocks at the end of the first period"
        );

        // it unlocks nothing during the period
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks all at the start of the last period
        uint256 vestingEndsAt = presale.getTgeTimestamp() + participant.cliffDuration
            + participant.vestingPeriodDuration * participant.vestingPeriodCount;
        vm.warp(vestingEndsAt);

        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        // it increases Membership usage
        assertEq(usage.max, participant.allocation, "usage.max equals to participant.allocation");
        assertEq(usage.current, participant.allocation, "usage.participant equals to participant.allocation");

        assertEq(tokenA.balanceOf(address(sender)), participant.allocation);

        // it unlocks nothing after that
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);
    }

    modifier givenTgeNumeratorGreaterThanZero() {
        participant.tgeNumerator = 10;
        _;
    }

    modifier givenCliffDurationEqualsZero() {
        participant.cliffDuration = 0;
        _;
    }

    function test_GivenNoCliffAndNoVestingPeriodCount()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        givenCurrentUsageLowerThanMax
        givenTgeNumeratorGreaterThanZero
        givenCliffDurationEqualsZero
    {
        participant.tgeNumerator = 100;
        participant.vestingPeriodCount = participant.vestingPeriodDuration = 0;

        _impersonate(participant);

        // it unlocks tge amount at tgeTimestamp
        // it emits Claimed event with Membership id and amountA
        vm.expectEmit(false, false, false, true);
        emit VestPresale.Claimed(0, participant.allocation);

        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        // it increases Membership usage
        assertEq(usage.max, participant.allocation, "usage.max equals to participant.allocation");
        assertEq(usage.current, participant.allocation, "usage.participant equals to participant.allocation");

        assertEq(tokenA.balanceOf(address(sender)), participant.allocation);

        // it unlocks nothing after that
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);
    }

    function test_GivenNoCliffAndVestingPeriodCountGreaterThanOne()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        givenCurrentUsageLowerThanMax
        givenTgeNumeratorGreaterThanZero
        givenCliffDurationEqualsZero
        asMember
    {
        // it unlocks tge amount at tgeTimestamp
        // it emits Claimed event with Membership id and amountA
        uint256 tge = participant.allocation * participant.tgeNumerator / participant.tgeDenominator;

        vm.expectEmit(false, false, false, true);
        emit VestPresale.Claimed(0, tge);

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), tge);

        // it unlocks nothing during the first period
        skip(participant.vestingPeriodDuration / 2);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks at the end of the first period
        skip(participant.vestingPeriodDuration / 2);

        uint256 amount = (participant.allocation - tge) / participant.vestingPeriodCount;

        vm.expectEmit(true, false, false, true);
        emit VestPresale.Claimed(membershipId, amount);

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), tge + amount);

        // it unlocks nothing during the period
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks all at the end of the last period
        uint256 vestingEndsAt = presale.getTgeTimestamp() + participant.cliffDuration
            + participant.vestingPeriodDuration * participant.vestingPeriodCount;
        vm.warp(vestingEndsAt);

        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        // it increases Membership usage
        assertEq(usage.max, participant.allocation, "usage.max equals to participant.allocation");
        assertEq(usage.current, participant.allocation, "usage.participant equals to participant.allocation");

        assertEq(tokenA.balanceOf(address(sender)), participant.allocation);

        // it unlocks nothing after that
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);
    }

    modifier givenCliffDurationIsGreaterThanZero() {
        participant.cliffDuration = 1 days;
        _;
    }

    modifier givenTgeAndCliffNumeratorIsZero() {
        participant.cliffNumerator = 0;
        _;
    }

    modifier givenVestingPeriodCountIsZero() {
        participant.vestingPeriodCount = 0;
        _;
    }

    function test_WhenNoPeriodsNoCliffNumeratorSequentialUnlocks()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        givenCurrentUsageLowerThanMax
        givenTgeNumeratorGreaterThanZero
        givenCliffDurationIsGreaterThanZero
        givenTgeAndCliffNumeratorIsZero
        givenVestingPeriodCountIsZero
        asMember
    {
        uint256 tge = participant.allocation * participant.tgeNumerator / participant.tgeDenominator;

        // it emits Claimed event with Membership id and amountA
        vm.expectEmit(false, false, false, true);
        emit VestPresale.Claimed(0, tge);

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), tge, "it unlocks tge amount at tgeTimestamp");

        // it unlocks nothing during the cliff
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        skip(participant.cliffDuration - 1);

        // it unlocks all at cliff end
        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        // it increases Membership usage
        assertEq(usage.max, participant.allocation, "usage.max equals to participant.allocation");
        assertEq(usage.current, participant.allocation, "usage.participant equals to participant.allocation");

        assertEq(tokenA.balanceOf(address(sender)), participant.allocation, "it unlocks all at cliff end");

        // it unlocks nothing after that
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);
    }

    modifier givenVestingPeriodCountIsGreaterThanZero() {
        _;
    }

    function test_WhenNoCliffNumeratorSequentialUnlocks()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        givenCurrentUsageLowerThanMax
        givenTgeNumeratorGreaterThanZero
        givenCliffDurationIsGreaterThanZero
        givenTgeAndCliffNumeratorIsZero
        givenVestingPeriodCountIsGreaterThanZero
        asMember
    {
        uint256 tge = participant.allocation * participant.tgeNumerator / participant.tgeDenominator;

        vm.expectEmit(false, false, false, true);
        emit VestPresale.Claimed(0, tge);

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), tge, "it unlocks nothing at tgeTimestamp");

        // it unlocks nothing during the cliff
        skip(participant.cliffDuration / 2);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        uint256 amount = (participant.allocation - tge) / participant.vestingPeriodCount;

        // it unlocks first period amount at cliff end
        skip(participant.cliffDuration / 2);

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), tge + amount, "it unlocks first period amount at cliff end");

        // it unlocks nothing during the period
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks at the end of the first period
        // it emits Claimed event with Membership id and amountA
        skip(participant.vestingPeriodDuration - 1);

        vm.expectEmit(true, false, false, true);
        emit VestPresale.Claimed(membershipId, amount);

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), tge + (amount * 2), "it unlocks at the end of the first period");

        // it unlocks nothing during the period
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks all at the end of the last period
        uint256 vestingEndsAt = presale.getTgeTimestamp() + participant.cliffDuration
            + participant.vestingPeriodDuration * participant.vestingPeriodCount;
        vm.warp(vestingEndsAt);

        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        // it increases Membership usage
        assertEq(usage.max, participant.allocation, "usage.max equals to participant.allocation");
        assertEq(usage.current, participant.allocation, "usage.participant equals to participant.allocation");

        assertEq(
            tokenA.balanceOf(address(sender)), participant.allocation, "it unlocks all at the end of the last period"
        );

        // it unlocks nothing after that
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);
    }

    function test_GivenCliffNumeratorIsGreaterThanZeroAndVestingPeriodCountIsGreaterThanOne()
        external
        givenTgeTimestampIsGreaterThanZero
        whenACallerOwnsTheMembership
        givenMembershipUsageMaxAndUsageCurrentGreaterThanZero
        givenBlockTimestampEqualsOrIsGreaterThanTgeTimestamp
        givenCurrentUsageLowerThanMax
        givenTgeNumeratorGreaterThanZero
        givenCliffDurationIsGreaterThanZero
    {
        participant.cliffNumerator = 10;

        _impersonate(participant);

        uint256 tge = participant.allocation * participant.tgeNumerator / participant.tgeDenominator;

        vm.expectEmit(false, false, false, true);
        emit VestPresale.Claimed(0, tge);

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), tge, "it unlocks nothing at tgeTimestamp");

        // it unlocks nothing during the cliff
        skip(participant.cliffDuration / 2);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        uint256 cliffUnlock = (participant.allocation * participant.cliffNumerator) / participant.cliffDenominator;

        // it unlocks cliff amount plus the first period amount at cliff end
        skip(participant.cliffDuration / 2);

        membershipId = presale.claim(membershipId);

        assertEq(
            tokenA.balanceOf(address(sender)),
            tge + cliffUnlock,
            "it unlocks cliff amount plus the first period amount at cliff end"
        );

        // it unlocks nothing during the period
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);

        // it unlocks at the period end
        skip(participant.vestingPeriodDuration - 1);

        uint256 installment = (participant.allocation - tge - cliffUnlock) / participant.vestingPeriodCount;

        vm.expectEmit(true, false, false, true);
        emit VestPresale.Claimed(membershipId, installment);

        membershipId = presale.claim(membershipId);

        assertEq(tokenA.balanceOf(address(sender)), tge + cliffUnlock + installment, "it unlocks at the period end");

        // it unlocks all at the end of the last period
        uint256 vestingEndsAt = presale.getTgeTimestamp() + participant.cliffDuration
            + participant.vestingPeriodDuration * participant.vestingPeriodCount;
        vm.warp(vestingEndsAt);

        membershipId = presale.claim(membershipId);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        // it increases Membership usage
        assertEq(usage.max, participant.allocation, "usage.max equals to participant.allocation");
        assertEq(usage.current, participant.allocation, "usage.participant equals to participant.allocation");

        assertEq(
            tokenA.balanceOf(address(sender)), participant.allocation, "it unlocks all at the end of the last period"
        );

        // it unlocks nothing after that
        skip(1);

        vm.expectRevert(abi.encodeWithSelector(VestPresale.ClaimNotAllowed.selector, membershipId));
        presale.claim(membershipId);
    }

    function test_UserWillNotLoseTokensDuringClaim() external givenTgeTimestampIsGreaterThanZero {
        membershipId = fixture_claim(1, alice);
        participant = participants.get(1, alice);
        vm.startPrank(alice);

        vm.warp(
            presale.getTgeTimestamp() + participant.cliffDuration
                + (participant.vestingPeriodDuration * (participant.vestingPeriodCount - 1)) - 1
        );

        membershipId = presale.claim(membershipId);

        assertNotEq(participant.allocation, tokenA.balanceOf(address(alice)));

        vm.warp(
            presale.getTgeTimestamp() + participant.cliffDuration
                + (participant.vestingPeriodDuration * (participant.vestingPeriodCount)) + 1
        );

        presale.claim(membershipId);

        assertEq(participant.allocation, tokenA.balanceOf(address(alice)));
    }
}
