// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_claim
├── when a caller does not own the Membership
│   └── it reverts
└── when a caller owns the Membership
    ├── given tgeTimestamp equals zero
    │   └── it reverts because users must retain an ability to refund until the tgeTimestamp is known
    └── given tgeTimestamp is greater than zero
        └── given Membership usage max and usage current greater than zero
            ├── given block timestamp is lower than tgeTimestamp
            │   └── it reverts because users must retain an ability to refund in case tgeTimestamp is moved into the future
            └── given block timestamp equals or is greater than tgeTimestamp
                ├── given Membership usage max or usage current equals zero
                │   └── it reverts because there is nothing to claim
                ├── given the caller is not a smart contract
                │   ├── it emits Claimed event with membership id and amountA
                │   ├── it increases nonRefundableTokenB based on Membership usage current and attributes price
                │   ├── it decreases Membership usage max by usage current
                │   ├── it zeros Membership usage current
                │   ├── it mints a new Membership to the caller
                │   ├── it a new Membership represents a title to the right amount of tokens
                │   ├── it releases tokenA
                │   └── it keeps track of Membership usage according to the amount of released tokenA
                ├── given the caller is a smart contract
                │   ├── given they do not implement the hook
                │   │   └── it reverts
                │   └── given they implement the hook
                │       ├── when they revert
                │       │   └── it reverts
                │       └── when they do not revert
                │           ├── when there is a reentrancy
                │           │   └── it reverts because Membership was already marked as non refundable
                │           └── when there is no reentrancy
                │               ├── it marks Membership as non refundable
                │               ├── it increases nonRefundableTokenB based on Membership usage current and attributes price
                │               ├── it decreases Membership usage max by usage current
                │               ├── it zeros Membership usage current
                │               ├── it emits Claimed event with the Membership id
                │               └── it mints a new Membership to the caller
                ├── given current usage equals max
                │   └── it unlocks nothing
                ├── given block timestamp after vesting ends
                │   └── it unlocks usage max minus usage current
                └── given current usage lower than max
                    ├── given tgeNumerator equals zero
                    │   ├── given no tge and no cliff
                    │   │   └── given vestingPeriodCount more than one
                    │   │       ├── it increases Membership usage
                    │   │       ├── it emits Claimed event with Membership id and amountA
                    │   │       ├── it unlocks nothing at tgeTimestamp
                    │   │       ├── it unlocks nothing during the first period
                    │   │       ├── it unlocks at the end of the first period
                    │   │       ├── it unlocks nothing during the period
                    │   │       ├── it unlocks all at the end of the last period
                    │   │       └── it unlocks nothing after that
                    │   └── given no tge and cliffDuration
                    │       ├── given no tge and cliffNumerator is zero
                    │       │   └── given flat cliff and vestingPeriodCount greater than one
                    │       │       ├── it increases Membership usage
                    │       │       ├── it emits Claimed event with Membership id and amountA
                    │       │       ├── it unlocks nothing at tgeTimestamp
                    │       │       ├── it unlocks nothing during the cliff
                    │       │       ├── it unlocks first period amount at cliff end
                    │       │       ├── it unlocks nothing during the first period
                    │       │       ├── it unlocks at the end of the first period
                    │       │       ├── it unlocks nothing during the period
                    │       │       ├── it unlocks all at the start of the last period
                    │       │       └── it unlocks nothing after that
                    │       └── given cliffNumerator is not zero
                    │           └── given cliff and vestingPeriodCount greater than one
                    │               ├── it increases Membership usage
                    │               ├── it emits Claimed event with Membership id and amountA
                    │               ├── it unlocks nothing at tgeTimestamp
                    │               ├── it unlocks nothing during the cliff
                    │               ├── it unlocks cliff amount on cliff end
                    │               ├── it unlocks nothing during the first period
                    │               ├── it unlocks at the end of the first period
                    │               ├── it unlocks nothing during the period
                    │               ├── it unlocks all at the start of the last period
                    │               └── it unlocks nothing after that
                    └── given tgeNumerator greater than zero
                        ├── given cliffDuration equals zero
                        │   ├── given no cliff and no vestingPeriodCount
                        │   │   ├── it increases Membership usage
                        │   │   ├── it emits Claimed event with Membership id and amountA
                        │   │   ├── it unlocks all at tgeTimestamp
                        │   │   └── it unlocks nothing after that
                        │   └── given no cliff and vestingPeriodCount greater than one
                        │       ├── it increases Membership usage
                        │       ├── it emits Claimed event with Membership id and amountA
                        │       ├── it unlocks tge amount at tgeTimestamp
                        │       ├── it unlocks nothing during the first period
                        │       ├── it unlocks at the end of the first period
                        │       ├── it unlocks nothing during the period
                        │       ├── it unlocks all at the end of the last period
                        │       └── it unlocks nothing after that
                        └── given cliffDuration is greater than zero
                            ├── given tge and cliffNumerator is zero
                            │   ├── given vestingPeriodCount is zero
                            │   │   └── when no periods no cliffNumerator sequential unlocks
                            │   │       ├── it increases Membership usage
                            │   │       ├── it emits Claimed event with Membership id and amountA
                            │   │       ├── it unlocks tge amount at tgeTimestamp
                            │   │       ├── it unlocks nothing during the cliff
                            │   │       ├── it unlocks all at cliff end
                            │   │       └── it unlocks nothing after that
                            │   └── given vestingPeriodCount is greater than zero
                            │       └── when no cliffNumerator sequential unlocks
                            │           ├── it increases Membership usage
                            │           ├── it emits Claimed event with Membership id and amountA
                            │           ├── it unlocks tge amount at tgeTimestamp
                            │           ├── it unlocks nothing during the cliff
                            │           ├── it unlocks the first period amount at cliff end
                            │           ├── it unlocks nothing during the period
                            │           ├── it unlocks at the period end
                            │           ├── it unlocks all at the start of the last period
                            │           └── it unlocks nothing after that
                            └── given cliffNumerator is greater than zero and vestingPeriodCount is greater than one
                                ├── it increases Membership usage
                                ├── it emits Claimed event with Membership id and amountA
                                ├── it unlocks tge amount at tgeTimestamp
                                ├── it unlocks nothing during the cliff
                                ├── it unlocks cliff amount plus the first period amount at cliff end
                                ├── it unlocks nothing during the period
                                ├── it unlocks at the period end
                                ├── it unlocks all at the start of the last period
                                └── it unlocks nothing after that
