// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestTest } from "test/VestTest.sol";
import { Participants } from "test/utils/Participants.sol";

contract Presale_withdrawTokenA is VestTest {
    using Participants for Participants.Collection;

    uint256 sold;
    uint256 liquidityA;
    uint256 membershipId;

    function setUp() public override {
        super.setUp();

        fixture(composeRounds());

        liquidityA = presale.liquidityA();
    }

    function test_GivenTheCallerIsNotBeneficiary() external {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));

        presale.withdrawTokenA(1 ether);
    }

    modifier givenTheCallerIsBeneficiary() {
        vm.startPrank(beneficiary);
        _;
    }

    function test_GivenNoTokensWereSold() external givenTheCallerIsBeneficiary {
        // it transfers all deposited tokenA to the beneficiary
        presale.withdrawTokenA(liquidityA);

        assertEq(presale.liquidityA(), 0);
        assertEq(tokenA.balanceOf(beneficiary), liquidityA);
    }

    modifier givenSomeTokensWereSold() {
        membershipId = fixture_buy(1, alice);
        sold += participants.get(1, alice).allocation;

        fixture_buy(1, bob);
        sold += participants.get(1, bob).allocation;
        _;
    }

    function test_GivenNoClaimbacksWereMade() external givenSomeTokensWereSold givenTheCallerIsBeneficiary {
        // it transfers all deposited tokenA to the beneficiary minus tokens sold minus the success fee
        presale.withdrawTokenA(presale.liquidityA());

        assertEq(presale.liquidityA(), 0);
        assertEq(tokenA.balanceOf(beneficiary), liquidityA - sold);
    }

    function test_GivenClaimbacksWereMade() external givenSomeTokensWereSold givenTheCallerIsBeneficiary {
        vm.stopPrank();

        Participants.Participant memory participant = participants.get(1, alice);

        vm.prank(alice);
        membershipId = presale.claimback(membershipId, participant.allocation);

        vm.startPrank(beneficiary);

        // it transfers all deposited tokenA to the beneficiary minus tokens sold plus tokens claimed back minus the success fee
        presale.withdrawTokenA(presale.liquidityA());

        assertEq(presale.liquidityA(), 0);
        assertEq(tokenA.balanceOf(beneficiary), liquidityA - sold + participant.allocation);
    }
}
