// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_withdrawTokenB
├── given tgeTimestamp is zero
│   └── it reverts because all users have the right to refund to prevent griefing
└── when block timestamp is on tgeTimestamp or later
    ├── given claimbackPeriod is zero
    │   ├── it transfers all tokenB to the beneficiary minus the protocol fee
    │   ├── it tracks how many tokenB the fee collector has available
    │   └── it emits an event
    └── given claimbackPeriod is not zero
        ├── when during the claimbackPeriod
        │   ├── given claims were made
        │   │   ├── it transfers all tokenB from claimed Memberships to the beneficiary minus the protocol fee
        │   │   ├── it tracks how many tokenB the fee collector has available
        │   │   ├── it emits an event
        │   │   └── it does not revert if new tokenB is available
        │   └── given no claims were made
        │       └── it reverts because there is no tokenB available for the beneficiary
        └── when after the claimbackPeriod
            ├── it transfers all tokenB to the beneficiary minus the protocol fee
            ├── it tracks how many tokenB the fee collector has available
            └── it emits an event
