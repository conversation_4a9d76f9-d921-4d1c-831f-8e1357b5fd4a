// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { Errors } from "src/libraries/Errors.sol";

import { Participants } from "test/utils/Participants.sol";
import { VestTest, Configuration } from "test/VestTest.sol";

contract Presale_withdrawTokenB is VestTest {
    using Participants for Participants.Collection;

    function setUp() public override {
        super.setUp();

        Configuration memory configuration = composeConfiguration();

        configuration.presale.claimbackPeriod = 0;
        configuration.presale.listingTimestamp = 0;

        fixture(configuration, composeRounds());

        fixture_buy(1, alice);
        fixture_buy(1, carol);
    }

    function test_GivenTgeTimestampIsZero() external {
        // it reverts because all users have the right to claimback to prevent griefing
        vm.expectRevert(Errors.UnacceptableValue.selector);

        presale.withdrawTokenB();
    }

    modifier whenBlockTimestampIsOnTgeTimestampOrLater() {
        vm.prank(beneficiary);
        presale.updateListingTimestamp(vm.unixTime() * 2);

        vm.warp(presale.getListingTimestamp() + 1);
        _;
    }

    function test_GivenClaimbackPeriodIsZero() external whenBlockTimestampIsOnTgeTimestampOrLater {
        uint256 total = presale.liquidityB();

        // it emits an event
        vm.expectEmit(false, false, false, true);
        emit VestPresale.WithdrawnB(total);

        presale.withdrawTokenB();

        assertEq(presale.liquidityB(), 0);

        uint256 fee = (total * fees.tokenBNumerator) / fees.tokenBDenominator;

        // it tracks how many tokenB the fee collector has available
        assertEq(tokenB.balanceOf(feeCollector), fee);

        // it transfers all tokenB to the beneficiary minus the protocol fee
        assertEq(tokenB.balanceOf(beneficiary), total - fee);
    }

    modifier givenClaimbackPeriodIsNotZero() {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.claimbackPeriod = 7 days;

        fixture(configuration, composeRounds());
        _;
    }

    modifier whenDuringTheClaimbackPeriod() {
        vm.warp(presale.getListingTimestamp() + 3 days);
        _;
    }

    function test_GivenClaimsWereMade()
        external
        whenBlockTimestampIsOnTgeTimestampOrLater
        givenClaimbackPeriodIsNotZero
        whenDuringTheClaimbackPeriod
    {
        fixture_buy(1, carol);
        fixture_claim(1, alice);

        uint256 liquidityB = presale.liquidityB();

        Participants.Participant memory participantC = participants.get(1, alice);

        uint256 withdrawable = participantC.allocation * participantC.price / 10 ** decimalsA;

        // it emits an event
        vm.expectEmit(false, false, false, true);
        emit VestPresale.WithdrawnB(withdrawable);

        presale.withdrawTokenB();

        assertEq(presale.liquidityB(), liquidityB - withdrawable);

        uint256 fee = (withdrawable * fees.tokenBNumerator) / fees.tokenBDenominator;

        // it tracks how many tokenB the fee collector has available
        assertEq(tokenB.balanceOf(feeCollector), fee);

        // it transfers all tokenB to the beneficiary minus the protocol fee
        assertEq(tokenB.balanceOf(beneficiary), withdrawable - fee);
    }

    function test_GivenNoClaimsWereMade()
        external
        whenBlockTimestampIsOnTgeTimestampOrLater
        givenClaimbackPeriodIsNotZero
        whenDuringTheClaimbackPeriod
    {
        // it reverts because there is no tokenB available for the beneficiary
        vm.expectRevert(Errors.UnacceptableValue.selector);

        presale.withdrawTokenB();
    }

    function test_WhenAfterTheClaimbackPeriod()
        external
        whenBlockTimestampIsOnTgeTimestampOrLater
        givenClaimbackPeriodIsNotZero
    {
        fixture_buy(1, carol);
        fixture_claim(1, alice);

        uint256 withdrawable = presale.liquidityB();

        // it emits an event
        vm.expectEmit(true, false, false, true);
        emit VestPresale.WithdrawnB(withdrawable);

        vm.warp(presale.getListingTimestamp() + presale.claimbackPeriod() + 1);

        presale.withdrawTokenB();

        assertEq(presale.liquidityB(), 0);

        uint256 fee = (withdrawable * fees.tokenBNumerator) / fees.tokenBDenominator;

        // it tracks how many tokenB the fee collector has available
        assertEq(tokenB.balanceOf(feeCollector), fee);

        // it transfers all tokenB to the beneficiary minus the protocol fee
        assertEq(tokenB.balanceOf(beneficiary), withdrawable - fee);
    }
}
