// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";

import { VestTest, Configuration } from "test/VestTest.sol";

contract Presale_updateTgeTimestamp is VestTest {
    uint256 timestamp;

    function setUp() public override {
        super.setUp();

        fixture();
    }

    function test_WhenTheCallerIsNotABeneficiary() external {
        vm.prank(chuck);
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));

        presale.updateTgeTimestamp(0);
    }

    modifier whenTheCallerIsABeneficiary() {
        vm.startPrank(beneficiary);
        _;
    }

    function test_WhenNewTimestampInThePast() external whenTheCallerIsABeneficiary {
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateTgeTimestamp(block.timestamp - 1);
    }

    modifier whenNewTimestampInTheFuture() {
        timestamp = block.timestamp + 1;
        _;
    }

    modifier whenListingTimestampIsZero() {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.listingTimestamp = 0;

        presale = fixture(configuration);
        _;
    }

    function test_WhenListingTimestampIsEqualsToZero()
        external
        whenListingTimestampIsZero
        whenTheCallerIsABeneficiary
        whenNewTimestampInTheFuture
    {
        // it emits TgeTimestampUpdated
        vm.expectEmit(false, false, false, true);
        emit VestPresale.TgeTimestampUpdated(timestamp);

        presale.updateTgeTimestamp(timestamp);

        // it modifies state
        assertEq(presale.getTgeTimestamp(), timestamp);
    }

    modifier whenListingTimestampIsNonZero() {
        _;
    }

    function test_WhenNewTimestampInLessThanListingTimestamp()
        external
        whenTheCallerIsABeneficiary
        whenNewTimestampInTheFuture
        whenListingTimestampIsNonZero
    {
        timestamp = presale.getListingTimestamp() - 1;

        // it emits TgeTimestampUpdated
        vm.expectEmit(false, false, false, true);
        emit VestPresale.TgeTimestampUpdated(timestamp);

        presale.updateTgeTimestamp(timestamp);

        // it modifies state
        assertEq(presale.getTgeTimestamp(), timestamp);
    }

    function test_WhenNewTimestampInGreaterThanListingTimestamp()
        external
        whenTheCallerIsABeneficiary
        whenNewTimestampInTheFuture
        whenListingTimestampIsNonZero
    {
        timestamp = presale.getListingTimestamp() + 1;

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateTgeTimestamp(timestamp);
    }
}
