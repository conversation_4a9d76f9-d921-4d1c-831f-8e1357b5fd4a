// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";

import { VestTest, Configuration } from "test/VestTest.sol";

contract Presale_updateListingTimestamp is VestTest {
    function setUp() public override {
        super.setUp();

        fixture();
    }

    function test_WhenTheCallerIsNotABeneficiary() external {
        // it reverts
        vm.prank(alice);
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, alice));

        presale.updateListingTimestamp(vm.unixTime() * 2);
    }

    modifier whenTheCallerIsABeneficiary() {
        vm.startPrank(beneficiary);
        _;
    }

    function test_WhenNewListingTimestampInThePastOrPresent() external whenTheCallerIsABeneficiary {
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateListingTimestamp(block.timestamp - 1);
    }

    function test_WhenNewListingTimestampInTheFuture() external whenTheCallerIsABeneficiary {
        uint256 timestamp = block.timestamp + presale.getTgeTimestamp();

        // TODO
        // it emits BatchMetadataUpdate(0, type(uint256).max) event on Membership

        // it emits ListingTimestampUpdated
        vm.expectEmit(true, false, false, false);
        emit VestPresale.ListingTimestampUpdated(timestamp);

        presale.updateListingTimestamp(timestamp);

        // it sets new listingTimestamp
        assertEq(presale.getListingTimestamp(), timestamp);
    }

    modifier whenTgeTimestampIsZero() {
        Configuration memory configuration = composeConfiguration();

        configuration.presale.tgeTimestamp = 0;
        configuration.presale.listingTimestamp = 0;

        presale = fixture(configuration);
        _;
    }

    function test_WhenTgeTimestampEqualsZero() external whenTgeTimestampIsZero whenTheCallerIsABeneficiary {
        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateListingTimestamp(block.timestamp + 1);
    }

    function test_WhenNewListingTimestampIsBeforeTgeTimestamp() external whenTheCallerIsABeneficiary {
        uint256 timestamp = presale.getTgeTimestamp() - 1;

        // it reverts
        vm.expectRevert(Errors.UnacceptableValue.selector);
        presale.updateListingTimestamp(timestamp);
    }

    function test_WhenNewListingTimestampIsAfterTgeTimestamp() external whenTheCallerIsABeneficiary {
        // TODO
        // it emits BatchMetadataUpdate(0, type(uint256).max) event on Membership

        uint256 timestamp = presale.getTgeTimestamp() + 1;

        // it emits ListingTimestampUpdated
        vm.expectEmit(true, false, false, false);
        emit VestPresale.ListingTimestampUpdated(timestamp);
        presale.updateListingTimestamp(timestamp);

        // it modifies state
        assertEq(presale.getListingTimestamp(), timestamp);
    }
}
