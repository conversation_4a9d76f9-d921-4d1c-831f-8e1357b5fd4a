Presale_updateListingTimestamp
  when the caller is not a beneficiary
    it reverts
  when the caller is a beneficiary
    when new listingTimestamp in the past or present
      it reverts
    when new listingTimestamp in the future
      it sets new listingTimestamp
      it emits ListingTimestampUpdated
      it emits BatchMetadataUpdate(0, type(uint256).max) event on Membership
    when tgeTimestamp equals zero
      it reverts
    when new listingTimestamp is before tgeTimestamp
      it reverts
    when new listingTimestamp is after tgeTimestamp
      it modifies state
      it emits ListingTimestampUpdated
      it emits BatchMetadataUpdate(0, type(uint256).max) event on Membership
