// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { VestTest } from "test/VestTest.sol";

contract Presale_getFeeCollector is VestTest {
    function test_ReturnsTheOwnerOfTheParentVestContract() external {
        presale = fixture();
        // it returns the owner of the parent Vest contract
        assertEq(presale.getFeeCollector(), feeCollector, "dev team is the fee collector");
        address newDevTeam = makeAddr("new dev team");
        vm.prank(feeCollector);
        controller.transferOwnership(newDevTeam);
        vm.prank(newDevTeam);
        controller.acceptOwnership();
        assertEq(presale.getFeeCollector(), newDevTeam, "new dev team is the fee collector");
    }
}
