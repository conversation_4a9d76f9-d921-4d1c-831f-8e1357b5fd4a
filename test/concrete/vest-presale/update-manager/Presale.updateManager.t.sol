// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";
import { Errors } from "src/libraries/Errors.sol";

import { VestTest } from "test/VestTest.sol";

contract Presale_updateManager is VestTest {
    function setUp() public override {
        super.setUp();

        fixture(composeRounds());
    }

    function test_WhenTheCallerIsNotAManager() external {
        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, chuck));
        presale.updateManager(chuck);
    }

    modifier whenTheCallerIsAManager() {
        vm.startPrank(manager);
        _;
    }

    function test_GivenTheValueIsZeroAddress() external whenTheCallerIsAManager {
        // it reverts
        vm.expectRevert(Errors.UnacceptableReference.selector);
        presale.updateManager(address(0));
    }

    function test_GivenTheValueIsValidAddress() external whenTheCallerIsAManager {
        address value = makeAddr("new-manager");

        // it emits ManagerUpdated event
        vm.expectEmit(true, false, false, false);
        emit VestPresale.ManagerUpdated(value);

        presale.updateManager(value);

        // it modifies manager address
        assertEq(value, presale.manager(), "it modifies manager address");
    }
}
