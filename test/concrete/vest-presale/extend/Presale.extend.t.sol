// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import { VestTest } from "test/VestTest.sol";
import { VestPresale } from "src/VestPresale.sol";
import { Round, RoundState } from "src/types/Round.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { Errors } from "src/libraries/Errors.sol";
import { Uint256Helper } from "test/utils/Uint256Helper.sol";
import { Participants } from "test/utils/Participants.sol";
import { ERC721AcceptingReceiver } from "test/samples/ERC721AcceptingReceiver.sol";
import { ERC721DynamicIds } from "src/utils/ERC721DynamicIds.sol";
import { DynamicIds } from "src/libraries/DynamicIds.sol";

contract Presale_extend is VestTest {
    using Uint256Helper for *;
    using SafeERC20 for IERC20;

    address eRC721AcceptingReceiver = address(new ERC721AcceptingReceiver());

    uint256 membershipId;

    using Participants for Participants.Collection;

    function setUp() public override {
        super.setUp();

        fixture(composeRounds());

        participants.add(
            1,
            eRC721AcceptingReceiver,
            0.1e18.decimals(decimalsB),
            100 ether.decimals(decimalsA),
            7 days,
            10,
            100,
            0,
            0,
            100,
            10,
            1 days
        );

        tokenB.safeTransfer(
            eRC721AcceptingReceiver, (100 ether.decimals(decimalsA) * 0.1e18.decimals(decimalsB)) / 10 ** decimalsA
        );

        bytes32 whitelistRoot = participants.getWhitelistRoot(1);

        vm.prank(manager);
        presale.updateWhitelist(1, whitelistRoot, "");
    }

    function test_GivenARoundIsInVestingState() external {
        uint256 roundId = 1;
        membershipId = fixture_buy(roundId, alice);

        vm.warp(presale.getRound(roundId).endTimestamp * 10);

        // it reverts
        vm.expectRevert(
            abi.encodeWithSelector(
                VestPresale.RoundStateMismatch.selector, roundId, RoundState.VESTING, RoundState.SALE
            )
        );

        // TODO update this in other tests in this file
        presale.extend(membershipId, 1);
    }

    modifier givenARoundIsInSaleState(uint256 roundId) {
        Round memory round = presale.getRound(roundId);
        vm.warp(round.startTimestamp);
        _;
    }

    function test_WhenAnAttackerUsesAMembershipTheyDoNotOwn() external givenARoundIsInSaleState(1) {
        uint256 roundId = 1;
        membershipId = fixture_buy(roundId, alice);

        vm.prank(chuck);

        // it reverts
        vm.expectRevert(abi.encodeWithSelector(Errors.AccountMismatch.selector, chuck));
        presale.extend(membershipId, 1);
    }

    function test_WhenTheCallerBuysWithTheMembershipTheyOwnAndThatIsBoughtOut() external givenARoundIsInSaleState(1) {
        membershipId = fixture_buy(1, alice);

        vm.startPrank(alice);

        // it reverts
        vm.expectRevert(); // there is no custom error
        presale.extend(membershipId, 1);
    }

    modifier whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(uint256 roundId, address account) {
        Participants.Participant memory participant = participants.get(roundId, account);

        membershipId = fixture_buy(1, account, participant.allocation / 2);

        vm.startPrank(account);
        _;
    }

    function test_GivenTheLiquidityAIsZero()
        external
        givenARoundIsInSaleState(1)
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(1, alice)
    {
        vm.stopPrank();

        // reset liquidityA to zero
        vm.startPrank(beneficiary);
        presale.withdrawTokenA(presale.liquidityA());

        vm.startPrank(alice);

        // it reverts
        vm.expectRevert(
            abi.encodeWithSelector(VestPresale.RoundStateMismatch.selector, 1, RoundState.VESTING, RoundState.SALE)
        );
        presale.extend(membershipId, 1);
    }

    function test_GivenTheMembershipHasZeroPrice()
        external
        givenARoundIsInSaleState(1)
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(1, bob)
    {
        Participants.Participant memory participant = participants.get(1, bob);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;
        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: participant.allocation, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it works without an allowance for tokenB
        membershipId = presale.extend(membershipId, amountA);

        // it modifies their Membership to reflect that purchase
        usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals participant allocation");
        assertEq(usage.current, 0, "usage.current equals claimed amount");
    }

    modifier givenTheMembershipHasNon_zeroPrice() {
        _;
    }

    function test_GivenTheCallerDidNotGiveAnAllowance()
        external
        givenARoundIsInSaleState(1)
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(1, alice)
        givenTheMembershipHasNon_zeroPrice
    {
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;

        // it reverts
        vm.expectRevert(); // error depends on token implementation (USDT on ethereum - Address.FailedInnerCall, default - IERC20Errors.ERC20InsufficientAllowance)
        presale.extend(membershipId, amountA);
    }

    function test_GivenTheCallerDoesNotHaveEnoughTokenB()
        external
        givenARoundIsInSaleState(1)
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(1, alice)
        givenTheMembershipHasNon_zeroPrice
    {
        Participants.Participant memory participant = participants.get(1, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;

        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        vm.stopPrank();
        tokenB.safeTransfer(alice, amountB / 2);

        vm.startPrank(alice);

        tokenB.forceApprove(address(presale), amountB);

        // it reverts
        vm.expectRevert(); // error depends on token implementation (USDT on ethereum - Address.FailedInnerCall, default - IERC20Errors.ERC20InsufficientBalance)
        presale.extend(membershipId, amountA);
    }

    function test_WhenTheCallerBuysMoreThanTheirMembershipAllowsFor()
        external
        givenARoundIsInSaleState(1)
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(1, alice)
        givenTheMembershipHasNon_zeroPrice
    {
        Participants.Participant memory participant = participants.get(1, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;
        uint256 amountB = (amountA * 2 * participant.price) / 10 ** decimalsA;

        vm.stopPrank();
        tokenB.safeTransfer(alice, amountB / 2);

        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);

        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: participant.allocation, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it buys less tokenA than they asked for
        // it pays with tokenB
        membershipId = presale.extend(membershipId, amountA * 2);

        // it modifies their Membership to reflect their purchase
        usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals participant allocation");
        assertEq(usage.current, 0, "usage.current equals claimed amount");
    }

    function test_WhenTheCallerBuysMoreThanTokenALiquidity()
        external
        givenARoundIsInSaleState(1)
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(1, alice)
        givenTheMembershipHasNon_zeroPrice
    {
        Participants.Participant memory participant = participants.get(1, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;

        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        vm.startPrank(beneficiary);
        presale.withdrawTokenA(presale.liquidityA());

        vm.stopPrank();
        tokenA.forceApprove(address(presale), amountA / 2);
        presale.depositTokenA(amountA / 2);

        tokenB.safeTransfer(alice, amountB);
        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);

        uint256 expectedBoughtAmount = amountA + (amountA / 2);
        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: expectedBoughtAmount, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it pays with tokenB
        membershipId = presale.extend(membershipId, amountA);

        // it modifies their Membership to reflect their purchase
        usage = membership.getUsage(membershipId);

        // it buys less tokenA than they asked for
        assertEq(usage.max, expectedBoughtAmount, "usage.max equals bought amount");
        assertEq(usage.current, 0, "usage.current equals claimed amount");
    }

    function test_WhenTheUserCallsExtendMultipleTimes(uint32 steps)
        external
        givenARoundIsInSaleState(1)
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(1, alice)
        givenTheMembershipHasNon_zeroPrice
    {
        vm.assume(steps >= 2 && steps <= 10);

        uint256 liquidityA = presale.liquidityA();
        uint256 liquidityB = presale.liquidityB();

        Participants.Participant memory participant = participants.get(1, alice);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;
        uint256 amountB = (amountA * participant.price) / 10 ** decimalsA;

        vm.stopPrank();
        tokenB.safeTransfer(alice, amountB);

        vm.startPrank(alice);
        tokenB.forceApprove(address(presale), amountB);

        uint256 rest = amountA;
        uint256 amount = amountA / steps;
        // it works every time
        for (uint256 i = 1; i <= steps - 1; i++) {
            (membershipId) = presale.extend(membershipId, amount);

            rest -= amount;
        }

        if (rest != 0) membershipId = presale.extend(membershipId, rest);

        assertEq(presale.liquidityB(), liquidityB + amountB, "liquidityA has been increased by amountB");
        assertEq(presale.liquidityA(), liquidityA - amountA, "liquidityA has been reduced by amountA");

        usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals amount bought");
        assertEq(usage.current, 0, "usage.current equals zero");
    }

    function test_GivenAUserIsASmartContract()
        external
        givenARoundIsInSaleState(1)
        whenTheCallerBuysWithTheMembershipTheyOwnAndThatIsNotBoughtOut(1, eRC721AcceptingReceiver)
    {
        Participants.Participant memory participant = participants.get(1, eRC721AcceptingReceiver);

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        uint256 amountA = usage.max - usage.current;
        tokenB.forceApprove(address(presale), (amountA * participant.price) / 10 ** decimalsA);

        uint256 mintId = DynamicIds.zeroLast16Bytes(membershipId);
        bytes memory payload = abi.encode(IVestMembership.Usage({ max: participant.allocation, current: 0 }));
        uint256 newPublicId = DynamicIds.createPublicId(membershipId, payload);

        // it emits DynamicIdNFTUpdated
        vm.expectEmit(true, true, false, true);
        emit ERC721DynamicIds.DynamicIdNFTUpdated(mintId, newPublicId, payload);

        // it buys tokenA
        // it pays with tokenB
        membershipId = presale.extend(membershipId, amountA);

        // it modifies their Membership to reflect that purchase
        usage = membership.getUsage(membershipId);

        assertEq(usage.max, participant.allocation, "usage.max equals participant allocation");
        assertEq(usage.current, 0, "usage.current equals claimed amount");
    }
}
