Presale_getRoundState
  given round does not exist
    it reverts
  given round exists
    when block timestamp is before round start timestamp
      it returns PENDING state
    when block timestamp is between round start and end timestamp
      given liquidityA is greater than zero
        it returns SALE state
      given liquidityA is zero
        it returns VESTING state
    when block timestamp is at end timestamp
      it returns VESTING state
    when block timestamp is after end timestamp
      it returns VESTING state
