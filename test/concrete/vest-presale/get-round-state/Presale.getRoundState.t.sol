// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import { VestPresale } from "src/VestPresale.sol";
import { Round, RoundState } from "src/types/Round.sol";

import { VestTest } from "test/VestTest.sol";
import { Uint256Helper } from "test/utils/Uint256Helper.sol";

contract Presale_getRoundState is VestTest {
    using Uint256Helper for *;
    using SafeERC20 for IERC20;

    Round internal round;

    function setUp() public override {
        super.setUp();

        Round[] memory rounds = new Round[](1);

        round = rounds[0] = Round({
            name: "ROUND I",
            startTimestamp: vm.unixTime(),
            endTimestamp: vm.unixTime() * 2,
            whitelistRoot: bytes32(0),
            proofsUri: ""
        });

        fixture(rounds);

        tokenA.forceApprove(address(presale), 1 ether.decimals(decimalsA));
        presale.depositTokenA(1 ether.decimals(decimalsA));
    }

    function test_GivenRoundDoesNotExist() external {
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundNotExists.selector, 2));

        // it reverts
        presale.getRound(2);
    }

    modifier givenRoundExists() {
        _;
    }

    function test_WhenBlockTimestampIsBeforeRoundStartTimestamp() external givenRoundExists {
        vm.warp(round.startTimestamp - 1);

        // it returns PENDING state
        RoundState state = presale.getRoundState(1);

        assertEq(uint256(state), uint256(RoundState.PENDING), "round has pending state");
    }

    modifier whenBlockTimestampIsBetweenRoundStartAndEndTimestamp() {
        vm.warp(round.startTimestamp + 1);
        _;
    }

    function test_GivenLiquidityAIsGreaterThanZero()
        external
        givenRoundExists
        whenBlockTimestampIsBetweenRoundStartAndEndTimestamp
    {
        // it returns SALE state
        RoundState state = presale.getRoundState(1);

        assertEq(uint256(state), uint256(RoundState.SALE), "round has sale state");
    }

    function test_GivenLiquidityAIsZero()
        external
        givenRoundExists
        whenBlockTimestampIsBetweenRoundStartAndEndTimestamp
    {
        vm.startPrank(beneficiary);
        presale.withdrawTokenA(presale.liquidityA());

        // it returns VESTING state
        RoundState state = presale.getRoundState(1);

        assertEq(uint256(state), uint256(RoundState.VESTING), "round has vesting state");
    }

    function test_WhenBlockTimestampIsAtEndTimestamp() external givenRoundExists {
        vm.warp(round.endTimestamp);

        // it returns VESTING state
        RoundState state = presale.getRoundState(1);

        assertEq(uint256(state), uint256(RoundState.VESTING), "round has vesting state");
    }

    function test_WhenBlockTimestampIsAfterEndTimestamp() external givenRoundExists {
        vm.warp(round.endTimestamp + 1);

        // it returns VESTING state
        RoundState state = presale.getRoundState(1);

        assertEq(uint256(state), uint256(RoundState.VESTING), "round has vesting state");
    }
}
