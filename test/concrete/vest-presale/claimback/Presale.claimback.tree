// THIS IS GENERATED AUTOMATICALLY. DO NOT EDIT DIRECTLY. SEE .outline FILE
Presale_claimback
├── given the caller does not own the Membership
│   └── it reverts
└── given the caller owns the Membership
    ├── when a user refunds zero tokens
    │   └── it reverts
    ├── when a membership claimbackPeriod attribute is equals to zero
    │   └── it reverts
    ├── given claimbackPeriod equals zero
    │   ├── given the listingTimestamp is zero
    │   │   ├── it refunds to prevent griefing
    │   │   ├── it emits Refunded event with Membership id and amountA
    │   │   ├── it transfers tokenB to the caller
    │   │   ├── it lowers liquidityB
    │   │   ├── it increases liquidityA
    │   │   └── it lowers the Membership usage
    │   └── given the listingTimestamp is not zero
    │       ├── given block timestamp is lower than tgeTimestamp
    │       │   ├── it refunds to prevent griefing
    │       │   ├── it emits Refunded event with Membership id and amountA
    │       │   ├── it transfers tokenB to the caller
    │       │   ├── it lowers liquidityB
    │       │   ├── it increases liquidityA
    │       │   └── it lowers the Membership usage
    │       ├── given block timestamp is after or at tgeTimestamp and before the listingTimestamp
    │       │   └── it reverts
    │       └── given block timestamp is after or at listingTimestamp
    │           └── it reverts
    └── given claimbackPeriod greater than zero
        ├── given block timestamp greater than the sum of listingTimestamp and presale claimbackPeriod
        │   └── it reverts because the refunds period passed
        ├── given block timestamp lower than or equal to the sum of listingTimestamp and presale claimbackPeriod
        │   ├── given the Membership was used to claim tokenA
        │   │   └── it reverts
        │   ├── when the caller refunds their entire usage
        │   │   ├── it refunds
        │   │   └── it emits Refunded event with Membership id and amountA
        │   └── given block timestamp greater than the sum of listingTimestamp and membership claimbackPeriod
        │       └── it reverts because the refunds period passed
        └── given block timestamp greater than the sum of listingTimestamp and presale claimbackPeriod and membership claimbackPeriod is greater than presale claimbackPeriod
            └── it reverts because the refunds period passed
