// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { VestTest } from "test/VestTest.sol";
import { Errors } from "src/libraries/Errors.sol";
import { VestPresale } from "src/VestPresale.sol";

contract Presale_updateWhitelist is VestTest {
    function setUp() public override {
        super.setUp();

        fixture(composeRounds());
    }

    function test_WhenTheCallerIsNotAManager() external {
        // it reverts
        vm.prank(alice);
        vm.expectRevert(abi.encodeWithSelector(Errors.Unauthorized.selector, alice));
        presale.updateWhitelist(1, "0x02", "https://example.com");
    }

    modifier whenTheCallerIsAManager() {
        vm.startPrank(manager);
        _;
    }

    function test_GivenTheRoundDoesNotExist() external whenTheCallerIsAManager {
        // it reverts
        vm.expectRevert(abi.encodeWithSelector(VestPresale.RoundNotExists.selector, 5));
        presale.updateWhitelist(5, "0x02", "https://example.com");
    }

    modifier givenTheRoundExists() {
        _;
    }

    function test_WhenRoundUnlocked() external whenTheCallerIsAManager {
        // it emits RoundUpdated event with round id
        vm.expectEmit(false, false, false, false);
        emit VestPresale.RoundUpdated(0);

        vm.warp(vm.unixTime() * 2 - 1 hours - 1);
        presale.updateWhitelist(1, "0x02", "https://example.com");

        // it updates whitelistRoot
        assertEq(presale.getRound(1).whitelistRoot, "0x02");

        // it updates proofsUri
        assertEq(presale.getRound(1).proofsUri, "https://example.com");
    }

    function test_WhenRoundLocked() external whenTheCallerIsAManager {
        // it emits RoundUpdated event with round id
        vm.expectEmit(false, false, false, false);
        emit VestPresale.RoundUpdated(0);

        vm.warp(vm.unixTime() * 2 - 1 hours + 1);
        presale.updateWhitelist(1, "0x02", "https://example.com");

        // it updates whitelistRoot
        assertEq(presale.getRound(1).whitelistRoot, "0x02");

        // it updates proofsUri
        assertEq(presale.getRound(1).proofsUri, "https://example.com");
    }
}
