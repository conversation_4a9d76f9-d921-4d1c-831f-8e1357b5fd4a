// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import { IERC20Metadata } from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";

import { Fees } from "src/types/Fees.sol";
import { Round } from "src/types/Round.sol";
import { Presale, Membership } from "src/types/Configuration.sol";
import { VestPresale } from "src/VestPresale.sol";
import { VestController } from "src/VestController.sol";
import { VestMembership } from "src/VestMembership.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { VestMembershipDescriptor } from "src/VestMembershipDescriptor.sol";

import { VestBuilder } from "script/Vest.builder.s.sol";

import { Test } from "./Test.sol";
import { Participants } from "./utils/Participants.sol";
import { Uint256Helper } from "./utils/Uint256Helper.sol";
import { ERC20Sample } from "./samples/ERC20Sample.sol";

struct Configuration {
    Presale.Configuration presale;
    Membership.Configuration membership;
}

contract VestTest is Test {
    using Uint256Helper for *;
    using SafeERC20 for IERC20;
    using Participants for Participants.Collection;

    Fees internal fees = Fees({
        tokenANumerator: 0,
        tokenADenominator: 1,
        tokenBNumerator: 10,
        tokenBDenominator: 100,
        nftNumerator: 100,
        nftDenominator: 10000
    });

    VestBuilder script;

    VestPresale internal presale;

    VestController internal controller;

    VestMembership internal membership;

    VestMembershipDescriptor internal membershipDescriptor = new VestMembershipDescriptor();

    uint8 internal decimalsA = uint8(vm.envOr("TEST_TOKEN_A_DECIMALS", uint8(18)));
    uint8 internal decimalsB = uint8(vm.envOr("TEST_TOKEN_B_DECIMALS", uint8(18)));

    IERC20 internal tokenA = composeTokenA();
    IERC20 internal tokenB = composeTokenB();

    address internal feeCollector = makeAddr("feeCollector");

    Participants.Collection internal participants;

    function setUp() public virtual {
        script = new VestBuilder();

        vm.allowCheatcodes(address(script));

        vm.startPrank(feeCollector);

        controller = script.init(fees);

        vm.stopPrank();
    }

    function fixture() public returns (VestPresale) {
        return fixture(new Round[](0));
    }

    function fixture(Round[] memory rounds) public returns (VestPresale) {
        return fixture(composeConfiguration(), rounds);
    }

    function fixture(Configuration memory configuration) public returns (VestPresale) {
        return fixture(configuration, composeRounds());
    }

    function fixture(Configuration memory configuration, Round[] memory rounds) public returns (VestPresale) {
        (address presale_, address membership_) = script.deploy(configuration.presale, rounds, configuration.membership);

        presale = VestPresale(payable(presale_));
        membership = VestMembership(membership_);

        uint256 deposit;
        for (uint256 i = 1; i <= rounds.length; i++) {
            if (participants.count(i) == 0) continue;

            deposit += participants.get(i, alice).allocation;
            deposit += participants.get(i, bob).allocation;
            deposit += participants.get(i, carol).allocation;
        }

        if (deposit == 0) return presale;

        deposit += ((deposit * fees.tokenANumerator) / fees.tokenADenominator);

        vm.startPrank(address(this));
        script.deposit(deposit);
        vm.stopPrank();

        return presale;
    }

    function composeRounds() internal returns (Round[] memory) {
        Round[] memory rounds = new Round[](3);

        participants.clear(1);
        participants.clear(2);
        participants.clear(3);

        uint256 timestamp = vm.unixTime();

        {
            // tge: 10%
            participants.add(
                1,
                alice,
                0.1e18.decimals(decimalsB),
                100 ether.decimals(decimalsA),
                7 days,
                10,
                100,
                0,
                0,
                100,
                10,
                1 days
            );

            // tge: 0%, price: 0
            participants.add(1, bob, 0, 200 ether.decimals(decimalsA), 7 days, 0, 100, 0, 0, 1, 10, 1 days);

            // tge: 0, cliff: 1 day
            participants.add(
                1,
                carol,
                0.1e18.decimals(decimalsB),
                200 ether.decimals(decimalsA),
                7 days,
                0,
                100,
                1 days,
                0,
                1,
                10,
                1 days
            );

            rounds[0] = Round({
                name: "ROUND I",
                startTimestamp: timestamp,
                endTimestamp: timestamp * 2,
                whitelistRoot: participants.getWhitelistRoot(1),
                proofsUri: ""
            });
        }

        {
            participants.add(
                2,
                alice,
                0.1e18.decimals(decimalsB),
                100 ether.decimals(decimalsA),
                7 days,
                10,
                100,
                0,
                0,
                1,
                10,
                1 days
            );
            participants.add(2, bob, 0, 200 ether.decimals(decimalsA), 7 days, 10, 100, 0, 0, 1, 10, 1 days);
            participants.add(
                2,
                carol,
                0.1e18.decimals(decimalsB),
                200 ether.decimals(decimalsA),
                7 days,
                10,
                100,
                0,
                0,
                1,
                10,
                1 days
            );

            rounds[1] = Round({
                name: "ROUND II",
                startTimestamp: timestamp * 3,
                endTimestamp: timestamp * 4,
                whitelistRoot: participants.getWhitelistRoot(2),
                proofsUri: ""
            });
        }

        {
            participants.add(
                3,
                alice,
                0.1e18.decimals(decimalsB),
                100 ether.decimals(decimalsA),
                7 days,
                10,
                100,
                0,
                0,
                1,
                10,
                1 days
            );
            participants.add(3, bob, 0, 200 ether.decimals(decimalsA), 7 days, 10, 100, 0, 0, 1, 10, 1 days);
            participants.add(
                3,
                carol,
                0.1e18.decimals(decimalsB),
                200 ether.decimals(decimalsA),
                7 days,
                10,
                100,
                0,
                0,
                1,
                10,
                1 days
            );

            rounds[2] = Round({
                name: "ROUND III",
                startTimestamp: timestamp * 5,
                endTimestamp: timestamp * 6,
                whitelistRoot: participants.getWhitelistRoot(3),
                proofsUri: ""
            });
        }

        return rounds;
    }

    function composeConfiguration() internal returns (Configuration memory) {
        return Configuration({
            presale: Presale.Configuration({
                tokenA: tokenA,
                tokenB: tokenB,
                manager: manager,
                beneficiary: beneficiary,
                claimbackPeriod: 7 days,
                tgeTimestamp: vm.unixTime(),
                listingTimestamp: vm.unixTime(),
                fees: Presale.Fees({
                    tokenANumerator: fees.tokenANumerator,
                    tokenADenominator: fees.tokenADenominator,
                    tokenBNumerator: fees.tokenBNumerator,
                    tokenBDenominator: fees.tokenBDenominator
                })
            }),
            membership: Membership.Configuration({
                fees: Membership.Fees({ numerator: fees.nftNumerator, denominator: fees.nftDenominator }),
                descriptor: membershipDescriptor,
                metadata: IVestMembership.Metadata({ token: address(tokenA), description: "", color: "" })
            })
        });
    }

    function composeTokenA() private returns (IERC20) {
        address token = vm.envOr("TEST_TOKEN_A_ADDRESS", address(0));

        if (!forking() || token == address(0)) return new ERC20Sample(uint8(decimalsA), 0);

        decimalsA = IERC20Metadata(token).decimals();

        address donor = vm.envOr("TEST_TOKEN_A_DONOR_ADDRESS", address(0));

        if (donor == address(0)) {
            deal(token, address(this), 100000 * 10 ** decimalsA);

            return IERC20(token);
        }

        vm.startPrank(donor);

        IERC20(token).transfer(address(this), IERC20(token).balanceOf(donor));

        vm.stopPrank();

        return IERC20(token);
    }

    function composeTokenB() private returns (IERC20) {
        address token = vm.envOr("TEST_TOKEN_B_ADDRESS", address(0));

        if (!forking() || token == address(0)) return new ERC20Sample(uint8(decimalsB), 0);

        decimalsB = IERC20Metadata(token).decimals();

        address donor = vm.envOr("TEST_TOKEN_B_DONOR_ADDRESS", address(0));

        if (donor == address(0)) {
            deal(token, address(this), 100000 * 10 ** decimalsB);

            return IERC20(token);
        }

        vm.startPrank(donor);

        IERC20(token).transfer(address(this), IERC20(token).balanceOf(donor));

        vm.stopPrank();

        return IERC20(token);
    }

    function fixture_buy(uint256 roundId, address account) internal returns (uint256) {
        Participants.Participant memory participant = participants.get(roundId, account);

        return fixture_buy(roundId, account, participant.allocation);
    }

    function fixture_buy(uint256 roundId, address account, uint256 amount) internal returns (uint256) {
        Round memory round = presale.getRound(roundId);

        Participants.Participant memory participant = participants.get(roundId, account);

        uint256 amountB = (amount * participant.price) / 10 ** decimalsA;

        vm.stopPrank();
        tokenB.safeTransfer(account, amountB);

        vm.warp(round.startTimestamp);

        vm.prank(account);
        tokenB.forceApprove(address(presale), amountB);

        IVestMembership.Attributes memory attributes = IVestMembership.Attributes({
            price: participant.price,
            allocation: participant.allocation,
            claimbackPeriod: participant.claimbackPeriod,
            tgeNumerator: participant.tgeNumerator,
            tgeDenominator: participant.tgeDenominator,
            cliffDuration: participant.cliffDuration,
            cliffNumerator: participant.cliffNumerator,
            cliffDenominator: participant.cliffDenominator,
            vestingPeriodCount: participant.vestingPeriodCount,
            vestingPeriodDuration: participant.vestingPeriodDuration,
            tradeable: participant.tradeable
        });

        vm.prank(account);
        return presale.buy(roundId, amount, attributes, participant.proof);
    }

    function fixture_claim(uint256 roundId, address account) internal returns (uint256) {
        uint256 membershipId = fixture_buy(roundId, account);

        vm.warp(presale.getTgeTimestamp());

        vm.prank(account);

        return presale.claim(membershipId);
    }
}
