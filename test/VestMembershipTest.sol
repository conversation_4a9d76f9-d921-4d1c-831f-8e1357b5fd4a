// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Membership } from "src/types/Configuration.sol";
import { VestMembership } from "src/VestMembership.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { IVestPresaleScheduler } from "src/IVestPresaleScheduler.sol";
import { IVestFeeCollectorProvider } from "src/IVestFeeCollectorProvider.sol";
import { VestMembershipDescriptor } from "src/VestMembershipDescriptor.sol";

import { Test } from "./Test.sol";
import { ERC20Sample } from "./samples/ERC20Sample.sol";

contract VestMembershipTest is Test {
    ERC20Sample internal token = new ERC20Sample(18, 0);

    address internal scheduler = makeAddr("dummy:scheduler");
    address internal feeCollectorProvider = makeAddr("dummy:feeCollectorProvider");

    VestMembership internal membership;

    VestMembershipDescriptor internal descriptor = new VestMembershipDescriptor();

    function fixture() internal returns (VestMembership) {
        Membership.Configuration memory configuration = Membership.Configuration({
            fees: composeFees(),
            descriptor: descriptor,
            metadata: IVestMembership.Metadata({
                token: address(token),
                description: "Example description",
                color: "#ffffff"
            })
        });

        return new VestMembership(
            address(this),
            IVestPresaleScheduler(scheduler),
            IVestFeeCollectorProvider(feeCollectorProvider),
            configuration
        );
    }

    function composeFees() internal pure returns (Membership.Fees memory) {
        return Membership.Fees({ numerator: 1, denominator: 10 });
    }
}
