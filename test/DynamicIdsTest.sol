// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Test } from "test/Test.sol";
import { ERC721DynamicIds } from "src/utils/ERC721DynamicIds.sol";
import { ERC721DynamicIdsMock } from "test/mocks/ERC721DynamicIdsMock.sol";
import { ERC721AcceptingReceiver } from "test/samples/ERC721AcceptingReceiver.sol";
import { IERC721Errors } from "@openzeppelin/contracts/interfaces/draft-IERC6093.sol";
import { IERC721 } from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import { DynamicIds } from "src/libraries/DynamicIds.sol";

import { ERC721Enumerable } from "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";

abstract contract DynamicIdsTest is Test {
    ERC721DynamicIdsMock collection = new ERC721DynamicIdsMock();
    ERC721AcceptingReceiver acceptingReceiver = new ERC721AcceptingReceiver();
    uint256 id;
}
