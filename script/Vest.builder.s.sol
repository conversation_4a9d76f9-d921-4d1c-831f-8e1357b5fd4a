// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";

import "forge-std/Script.sol";
import { VmSafe } from "forge-std/Vm.sol";

import { Round } from "src/types/Round.sol";
import { Fees } from "src/types/Fees.sol";
import { Presale, Membership } from "src/types/Configuration.sol";
import { VestPresale } from "src/VestPresale.sol";
import { VestController } from "src/VestController.sol";
import { VestPresaleDeployer } from "src/VestPresaleDeployer.sol";
import { VestMembershipDeployer } from "src/VestMembershipDeployer.sol";

contract VestBuilder is Script {
    VestPresale internal presale;

    VestController internal controller;

    modifier callerInterceptor() {
        (VmSafe.CallerMode mode, address caller,) = vm.readCallers();

        bool pranked = mode == VmSafe.CallerMode.Prank || mode == VmSafe.CallerMode.RecurrentPrank;

        if (pranked) vm.startPrank(caller);

        _;
    }

    function init(Fees memory fees) public callerInterceptor returns (VestController) {
        controller = new VestController(new VestPresaleDeployer(), new VestMembershipDeployer(), fees);

        return controller;
    }

    function deploy(
        Presale.Configuration memory presaleConfig,
        Round[] memory rounds,
        Membership.Configuration memory membershipConfig
    ) public callerInterceptor returns (address, address) {
        require(address(controller) != address(0));

        (address presale_, address membership) = controller.createPresale(presaleConfig, rounds, membershipConfig);

        presale = VestPresale(payable(presale_));

        return (presale_, membership);
    }

    function deposit(uint256 amount) public callerInterceptor {
        require(address(presale) != address(0));

        SafeERC20.forceApprove(presale.tokenA(), address(presale), amount);

        presale.depositTokenA(amount);
    }
}
