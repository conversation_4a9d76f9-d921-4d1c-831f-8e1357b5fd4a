// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import "forge-std/Script.sol";

import { Round } from "src/types/Round.sol";
import { VestPresale } from "src/VestPresale.sol";

contract RaiinmakerAddFCFSRound is Script {
    function run() external {
        vm.startBroadcast();

        VestPresale presale = VestPresale(payable(0x04aAC06e8f5f2D6f76177819C9fd69736FdbF7f2));

        presale.addRound(
            Round({
                name: "FCFS",
                startTimestamp: **********,
                endTimestamp: **********,
                whitelistRoot: bytes32(0x9c2941ff1a014dac37e0e7a6911a50b38f3f4cba2aa995a912d0d1b28b412dc9),
                proofsUri: ""
            })
        );

        vm.stopBroadcast();
    }
}
