// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { ERC20 } from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

import "forge-std/Script.sol";

import { Round } from "src/types/Round.sol";
import { Fees } from "src/types/Fees.sol";
import { Presale, Membership } from "src/types/Configuration.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { VestMembershipDescriptor } from "src/VestMembershipDescriptor.sol";

import { VestBuilder } from "script/Vest.builder.s.sol";

import { Raiinmaker } from "script/one-off/rain-coin.sol";

contract RaiinmakerDevelopment is VestBuilder {
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");

        vm.startBroadcast(deployerPrivateKey);

        ERC20 tokenA = new Raiinmaker(18);

        Fees memory fees = Fees({
            tokenANumerator: 0,
            tokenADenominator: 1,
            tokenBNumerator: 1,
            tokenBDenominator: 10,
            nftNumerator: 1,
            nftDenominator: 10
        });

        init(fees);

        Round memory round1 = Round({
            name: "TGLP",
            startTimestamp: 1712052000, // Tuesday, 2 April 2024 10:00:00 UTC
            endTimestamp: 1712138400, // Wednesday, 3 April 2024 10:00:00 UTC
            whitelistRoot: bytes32(0xc2864028dc84311daecbe4e7313d52e5bd7cc620ee04450ab70f6f3496dde49c),
            proofsUri: "https://example.com/"
        });

        Round[] memory rounds = new Round[](1);
        rounds[0] = round1;

        deploy(
            Presale.Configuration({
                tokenB: IERC20(0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48),
                tokenA: IERC20(tokenA),
                manager: 0xe5dFEc66E2F0ea5914E16035cEf00c5980D9B12B, // NOT SURE
                beneficiary: 0x547fbB770e62C885388D5F588856Fe722f7324D3, // NOT SURE
                listingTimestamp: 0,
                tgeTimestamp: 0,
                claimbackPeriod: 604_800,
                fees: Presale.Fees({
                    tokenANumerator: fees.tokenANumerator,
                    tokenADenominator: fees.tokenADenominator,
                    tokenBNumerator: fees.tokenBNumerator,
                    tokenBDenominator: fees.tokenBDenominator
                })
            }),
            rounds,
            Membership.Configuration({
                fees: Membership.Fees({ numerator: fees.nftNumerator, denominator: fees.nftDenominator }),
                descriptor: new VestMembershipDescriptor(),
                metadata: IVestMembership.Metadata({ token: address(tokenA), description: "TGLP vesting", color: "#ffffff" })
            })
        );

        deposit(100_000 * 10 ** 18);

        vm.stopBroadcast();
    }
}
