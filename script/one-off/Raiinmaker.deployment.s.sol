// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { ERC20 } from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import { IERC20 } from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

import "forge-std/Script.sol";

import { Round } from "src/types/Round.sol";
import { Fees } from "src/types/Fees.sol";
import { Presale, Membership } from "src/types/Configuration.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { VestMembershipDescriptor } from "src/VestMembershipDescriptor.sol";

import { VestBuilder } from "script/Vest.builder.s.sol";

contract RaiinmakerDeployment is VestBuilder {
    address internal constant TOKEN_A = 0x481FE356DF88169f5F38203Dd7f3C67B7559FDa5;
    address internal constant TOKEN_B = 0x833589fCD6eDb6E08f4c7C32D4f71b54bdA02913;

    function run() external {
        vm.startBroadcast();

        Fees memory fees = Fees({
            tokenANumerator: 0,
            tokenADenominator: 1,
            tokenBNumerator: 1,
            tokenBDenominator: 10,
            nftNumerator: 1,
            nftDenominator: 10
        });

        init(fees);

        Round memory round1 = Round({
            name: "TGLP",
            startTimestamp: 1712052000, // Tuesday, 2 April 2024 10:00:00 UTC
            endTimestamp: 1712138400, // Wednesday, 3 April 2024 10:00:00 UTC
            whitelistRoot: bytes32(0x593a3262a22d6b8b6cddd06fb2677450d857c836efc36f00c45ddc63d7d50f82),
            proofsUri: ""
        });

        Round[] memory rounds = new Round[](1);
        rounds[0] = round1;

        deploy(
            Presale.Configuration({
                tokenA: IERC20(TOKEN_A),
                tokenB: IERC20(TOKEN_B),
                manager: 0x6f2314cE5Df204Fa86d809D6eBD452c99976ea86,
                beneficiary: 0x04bd05AD6792c20Eb894b46a0E218c4976742653,
                tgeTimestamp: 0,
                listingTimestamp: 0,
                claimbackPeriod: 604_800,
                fees: Presale.Fees({
                    tokenANumerator: fees.tokenANumerator,
                    tokenADenominator: fees.tokenADenominator,
                    tokenBNumerator: fees.tokenBNumerator,
                    tokenBDenominator: fees.tokenBDenominator
                })
            }),
            rounds,
            Membership.Configuration({
                fees: Membership.Fees({ numerator: fees.nftNumerator, denominator: fees.nftDenominator }),
                descriptor: new VestMembershipDescriptor(),
                metadata: IVestMembership.Metadata({ token: address(TOKEN_A), description: "TGLP vesting", color: "#ffffff" })
            })
        );

        vm.stopBroadcast();
    }
}
