// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { ERC20 } from "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract Raiinmaker is ERC20 {
    uint8 private _decimals;

    constructor(uint8 decimals_) ERC20("<PERSON><PERSON><PERSON>", "Coiin") {
        _decimals = decimals_;

        _mint(msg.sender, 10000000 * 10 ** decimals_);
    }

    function decimals() public view virtual override returns (uint8) {
        return _decimals;
    }
}