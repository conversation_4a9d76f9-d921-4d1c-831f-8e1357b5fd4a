// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import "forge-std/Script.sol";

import { VestPresale } from "src/VestPresale.sol";
import { Round } from "src/types/Round.sol";

contract PresaleDevelopment is Script {
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");

        vm.startBroadcast(deployerPrivateKey);

        VestPresale presale = VestPresale(payable(0xF7CaAD9fc3fd3a42817477dcAA0b3E8FD06f5A42));

        presale.addRound(
            Round({
                name: "FCFS",
                startTimestamp: **********,
                endTimestamp: **********,
                whitelistRoot: 0xd16210002fcb4212853dcf80bcf2e4d48d7b8695d85c1edce8650c7b29d2e1b3,
                proofsUri: "https://presale.ivendpay.com"
            })
        );

        vm.stopBroadcast();
    }
}
