// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import "forge-std/Script.sol";

import { VestPresale } from "src/VestPresale.sol";

contract PresaleDevelopment is Script {
    function run() external {
        uint256 deployerPrivateKey = vm.envUint("PRIVATE_KEY");

        vm.startBroadcast(deployerPrivateKey);

        VestPresale presale = VestPresale(payable(0xF7CaAD9fc3fd3a42817477dcAA0b3E8FD06f5A42));

        presale.updateListingTimestamp(**********);

        vm.stopBroadcast();
    }
}
