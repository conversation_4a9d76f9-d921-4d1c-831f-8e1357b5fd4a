# justfile documentation https://just.systems/man/en/chapter_1.html
# justfile cheatsheet https://cheatography.com/linux-china/cheat-sheets/justfile/

# TODO
# a command for each test type
# commands for deployment / maintenance scripts

default:
  @just --choose

install:
  forge install

lint:
  #!/bin/bash
  set -euxo pipefail
  # https://www.linuxjournal.com/content/globstar-new-bash-globbing-option
  shopt -s globstar
  npx solhint test/**/*.sol --config test/.solhint.json
  npx solhint src/**/*.sol --config src/.solhint.json

document:
  forge doc

read-docs:
  forge doc --serve

setup:
  #!/bin/bash
  npm
  forge install
  forge compile

alias format := fix
fix:
  #!/bin/bash
  set -euxo pipefail
  # https://www.linuxjournal.com/content/globstar-new-bash-globbing-option
  shopt -s globstar
  npx solhint test/**/*.sol --config test/.solhint.json --fix --noPrompt
  npx solhint src/**/*.sol --config src/.solhint.json --fix --noPrompt
  forge fmt

build:
  forge build

test:
  forge test

static-analysis:
  slither . --config-file slither.config.json

gas:
  forge snapshot

chain:
  anvil

tree:
  #!/bin/bash
  set -euxo pipefail
  # https://www.linuxjournal.com/content/globstar-new-bash-globbing-option
  shopt -s globstar
  npm run tree
  bulloak scaffold ./test/concrete/**/*.tree -s 0.8.23 -w

outline:
  npm run outline-tests

check-tree:
  #!/bin/bash
  set -euxo pipefail
  # https://www.linuxjournal.com/content/globstar-new-bash-globbing-option
  shopt -s globstar
  bulloak check ./test/concrete/**/*.tree

fix-tree:
  #!/bin/bash
  set -euxo pipefail
  # https://www.linuxjournal.com/content/globstar-new-bash-globbing-option
  shopt -s globstar
  bulloak check ./test/concrete/**/*.tree --fix
  forge fmt

mutation-test:
  vertigo run

formal-verification-presale:
  certoraRun test/formal-verification/conf/presale.conf

formal-verification-vest:
  certoraRun test/formal-verification/conf/vest.conf

formal-verification:
  certoraRun test/formal-verification/conf/vest.conf
  certoraRun test/formal-verification/conf/presale.conf

invariant-test:
  echidna echidna/TestPresale.sol --contract TestPresale --config echidna.config.yaml

assertion-test:
  echidna echidna/TestPresale.sol --contract TestPresale --config echidna.config.yaml --test-mode assertion

overflow-test:
  echidna echidna/TestPresale.sol --contract TestPresale --config echidna.config.yaml --test-mode overflow

deploy:
  # https://github.com/foundry-rs/foundry/issues/4329#issuecomment-1509282645
  rm -rf out
  forge script script/Presale.production.s.sol --rpc-url bsc_mainnet -vvvv --tc PresaleDevelopment --broadcast --verify

updateListingTimestamp:
  forge script script/Presale.updateListingTimestamp.s.sol:PresaleDevelopment -vvvv --rpc-url bsc_mainnet --broadcast
