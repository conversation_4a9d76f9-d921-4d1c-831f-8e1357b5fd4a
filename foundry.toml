[profile.default]
src = "src"
out = "out"
libs = ["lib"]
fs_permissions = [{ access = "read-write", path = "file-snapshots" }]

gas_reports = ["Presale", "Membership", "Vest"]

[fmt]
bracket_spacing = true

[rpc_endpoints]
localhost = "http://localhost:8545"
bsc_testnet = "https://data-seed-prebsc-1-s1.binance.org:8545/"
bsc_mainnet = "https://bsc-dataseed1.binance.org/"
patryk_phalcon = "https://rpc.phalcon.blocksec.com/rpc_a233c68f0ac24f43843babb65670528c"
luk_phalcon = "https://rpc.phalcon.blocksec.com/rpc_9e32610112124354b0bce8e88ebf84a9"
base = "https://base.llamarpc.com"

[etherscan]
bsc_testnet = { key = "${BSCSCAN_API_KEY}", url = "https://api.bscscan.com/api" }
bsc_mainnet = { key = "${BSCSCAN_API_KEY}", url = "https://api.bscscan.com/api" }
base = { key = "${BSCSCAN_API_KEY}", url = "https://api.basescan.org/api", chain = 8453 }
patryk_phalcon = { key = "407a6a11-f2d8-4793-b11f-1a4f009e2456", url = "https://api.phalcon.xyz/api/rpc_a233c68f0ac24f43843babb65670528c", chain=999111 }
luk_phalcon = { key = "407a6a11-f2d8-4793-b11f-1a4f009e2456", url = "https://api.phalcon.xyz/api/rpc_9e32610112124354b0bce8e88ebf84a9", chain=1 }
# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options

# forge script script/one-off/ImpersonateIvendPayMigration.s.sol --rpc-url phalcon -vvvv ImpersonateIvendPayMigration --broadcast --unlocked --sender ****************************************** --verify --slow --chain 56 --etherscan-api-key "816d17ad-e797-420c-8a2d-bad74947568f" --verifier-url 'https://api.phalcon.xyz/api/rpc_97d68175de844e85b742ce2e49beedcd'

# https://rpc.phalcon.blocksec.com/rpc_5aefa145202e4bc399e69fba7149613a
# forge script script/one-off/raiinmaker.s.sol:RaiinmakerDevelopment --rpc-url patryk_phalcon -vvvv --broadcast --verify --slow
# forge script script/one-off/raiinmaker.s.sol:RaiinmakerDevelopment --rpc-url luk_phalcon -vvvv --broadcast --verify --slow
