diff --git a/node_modules/@openzeppelin/contracts/token/ERC721/ERC721.sol b/node_modules/@openzeppelin/contracts/token/ERC721/ERC721.sol
index 98a80e5..70cc84a 100644
--- a/node_modules/@openzeppelin/contracts/token/ERC721/ERC721.sol
+++ b/node_modules/@openzeppelin/contracts/token/ERC721/ERC721.sol
@@ -462,7 +462,7 @@ abstract contract ERC721 is Context, ERC165, IERC721, IERC721Metadata, IERC721Er
      * @param tokenId uint256 ID of the token to be transferred
      * @param data bytes optional data to send along with the call
      */
-    function _checkOnERC721Received(address from, address to, uint256 tokenId, bytes memory data) private {
+    function _checkOnERC721Received(address from, address to, uint256 tokenId, bytes memory data) internal {
         if (to.code.length > 0) {
             try IERC721R<PERSON>eiver(to).onERC721Received(_msgSender(), from, tokenId, data) returns (bytes4 retval) {
                 if (retval != IERC721Receiver.onERC721Received.selector) {
