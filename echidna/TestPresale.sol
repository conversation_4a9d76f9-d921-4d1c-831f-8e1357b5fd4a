// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Setup } from "./Setup.sol";

contract TestPresale is Setup {
    function echidna_address_of_deployed_test_contract() public view returns (bool) {
        return address(this) == 0x00a329c0648769A73afAc7F9381E08FB43dBEA72;
    }

    function echidna_liquidityA_cannot_be_higher_than_balanceA() public view returns (bool) {
        return presale.liquidityA() <= tokenA.balanceOf(address(this));
    }

    function echidna_liquidityB_cannot_be_higher_than_balanceB() public view returns (bool) {
        return presale.liquidityB() <= tokenB.balanceOf(address(this));
    }

    function echidna_current_usage_cannot_be_higher_than_max_usage_sale_membership() public view returns (bool) {
        return presale.membership().getUsage(1).current <= presale.membership().getUsage(1).max;
    }

    function echidna_current_usage_cannot_be_higher_than_max_usage_vesting_membership() public view returns (bool) {
        return presale.membership().getUsage(1).current <= presale.membership().getUsage(1).max;
    }
}
