# ⛓️ Vest EVM

This is an EVM Vesting Protocol we use to setup presales and vestings.

This should be documented, implemented, tested, audited, and deployed to various networks to the same address as if it was a public facing product.

- [⛓️ Vest EVM](#️-vest-evm)
  - [Business requirements](#business-requirements)
    - [Actors](#actors)
    - [Supported tokens](#supported-tokens)
    - [Meta tokens](#meta-tokens)
    - [Access](#access)
    - [Token listing](#token-listing)
    - [Refunds](#refunds)
    - [Capital collected](#capital-collected)
    - [Tradeability](#tradeability)
    - [Vesting rights](#vesting-rights)
    - [Fees](#fees)
    - [Flexibility](#flexibility)
    - [Presale creation](#presale-creation)
    - [Vesting schedule](#vesting-schedule)
    - [Max token allocation](#max-token-allocation)
    - [Rounds](#rounds)
    - [Unsold tokens](#unsold-tokens)
    - [Price increase](#price-increase)
    - [Access transferability (v2 idea, don’t implement)](#access-transferability-v2-idea-dont-implement)
    - [Depositing FOO](#depositing-foo)
- [Project setup](#project-setup)
  - [Requirements](#requirements)
  - [Setup](#setup)
  - [Tests](#tests)
    - [Echidna tests](#echidna-tests)
    - [Types of tests we use in this project](#types-of-tests-we-use-in-this-project)
    - [Testing tools we use in this project](#testing-tools-we-use-in-this-project)
    - [When to write which tests](#when-to-write-which-tests)
    - [Formal verification](#formal-verification)
    - [Static Analysis](#static-analysis)
    - [Tokens](#tokens)
    - [Local anvil snapshots](#local-anvil-snapshots)
- [Frontend integration guide](#frontend-integration-guide)
  - [Known data](#known-data)
  - [The first render (wallet not connected)](#the-first-render-wallet-not-connected)
  - [After the wallet is connected](#after-the-wallet-is-connected)
    - [For pending rounds (round hasn’t started yet)](#for-pending-rounds-round-hasnt-started-yet)
    - [For active rounds](#for-active-rounds)
    - [For inactive rounds (round ended)](#for-inactive-rounds-round-ended)
- [DX (Developer Experience) TODO](#dx-developer-experience-todo)
- [TODO](#todo)
  - [Towards internal v1](#towards-internal-v1)
    - [Later if needed](#later-if-needed)
  - [Towards external v1](#towards-external-v1)
  - [Won’t implement](#wont-implement)

## Business requirements

### Actors

- Beneficiary: an issuer of an ERC-20 token (TokenA) wanting to presale this token for a predefined amount of another ERC-20 token (TokenB). Different for each presale.
- Presale manager: sets up and manages a presale for the Beneficiary. They can setup presale parameters, update root hash during the presale, etc. They can’t receive any funds, meaning they have no custody over Beneficiary’s funds. Different for each presale.
- User: presale participant. They provide TokenB in exchange for a right to receive TokenA over time.
- Dev Team: develops and deploys the Vest protocol.
- Fee collector: an address that collects fees on TokenA, TokenB, and NFT royalties.

### Supported tokens

Token A: an ERC-20 token issued by the Beneficiary. The rest of this documentation refers to Token A as FOO.
Token B: any ERC-20 token. The rest of this documentation refers to Token B as USDT.

Native chain coins (eg. ETH) are not supported. Use wrapped tokens if needed.

Token A and Token B are called a token pair.

The protocol does not support ERC-20 tokens with fees on transfer.

### Meta tokens

- **pFOO**: represents the right to access the presale and on what conditions (price, vesting schedule, max allocation, etc). ⚠️ We will probably not implement it because:
  - we don’t want to allow people to trade their access to presales
  - another way to designate a different wallet for a particular presale would be by signing a message off-chain. It’s cheaper and more universal (supports non-EVM chains).
- **SaleMembership**: represents the right to refund FOO.

  When SaleMembership is minted, we don’t know definitively for how long it will be refundable, as this time can be prolonged.

  Immutable properties:

  - `allocation`—the max amount of FOO that can be bought
  - `price`—how much USDT does one FOO cost?
  - `refundableUnit`—a User can claim or refund an amount that is a multiplication of `refundableUnit` unless the max amount they can claim is lower than `refundableUnit`.
  - Vesting
    - `vestingPeriodDuration`—how long does one vesting period duration last in seconds
    - `vestingPeriodCount`—how many vesting periods there are
    - `tgeNumerator` and `tgeDenominator`—percentage of tokens unlocked on TGE (Token Generation Event). Invariants:
      - `tgeDenominator ≥ tgeNumerator`
      - `tgeDenominator > 0`
    - `cliffDuration`—how long does the cliff last in seconds (the width). Zero if no cliff.
    - `cliffNumerator` and `cliffDenominator`—how big the cliff is (the height). Invariants:
      - `cliffDenominator ≥ cliffNumerator`
      - `cliffDenominator > 0`

  Mutable properties:

  - `bought`—the amount of FOO bought. Can never be greater than `allocation`

- **vtFOO**: represents the right to claim vested FOO tokens.

  Immutable properties:

  - `amount`—how much FOO tokens are vested in total. This is a copy of `bought` amount from `SaleMembership`
  - All Vesting properties from `SaleMembership` get copied.

  Mutable properties

  - `claimed`—amount of FOO claimed using this `vtFOO`. If `tgeNumerator > 0`, `vtFOO` should be minted with `claimed` equal to the amount of `FOO` released during the tge. Invariants:
    - `amount ≥ claimed`

### Access

Only whitelisted Users can access the presale.

The smart contract uses Merkle Tree mechanism—it holds both the tree root and a url where proofs can be found.

Update of these two fields (tree root and proofs url) must be atomic.

### Token listing

If the beneficiary knows ahead of time when the token gets listed on an exchange, they can pass `listingTimestamp` during presale creation.

If not, they can leave `listingTimestamp` set to `0` and update it later through `updateListingTimestamp` method.

When `listingTimestamp` is `0`:

- Users can’t receive `tokenA`
- Users can’t `claimWithSaleMembership` so no user should have `VestingMembership` minted
- Beneficiary can’t receive any `tokenB`
- Users can `refund` any time

Once `listingTimestamp` is set to a value other than `0`, it can never be set to a value lower than `block.timestamp`.

### Refunds

Users can swap back the entirety of their Token B for the Token A for up to N days of a Token A listing date provided they didn’t claim any of their Token A yet.

Only the address entitled to claim their tokens can trigger a refund. In other words, address A can’t trigger a refund for address B.

The Vest protocol can’t know if TokenA was already listed. In the case of postponed listing, the Beneficiary and Presale Manager are incentivised to update the listing timestamp to a future date to keep all Users happy.

If they don’t update the listing timestamp, Users have an incentive to get a refund as the other side of the deal is not being kept.

⚠️ This feature is optional, there can be non-refundable presales.

### Capital collected

The Beneficiary can only withdraw capital collected from users if:

- It’s past the N days of the token refund period
- or the user claimed their Token A and therefore lost their right for the refund

Anyone can trigger the withdrawal that gets automatically transferred to a predefined Beneficiary’s wallet address.

### Tradeability

The Beneficiary may want to prevent users from being able to setup their own trading pair on a DEX before the official listing happens.

The Beneficiary can configure and later extend the block period during which Users can’t withdraw Token A.

As long as Token A is blocked, Users can refund it for Token B, even if the presale is non-refundable.

The Beneficiary can’t block tokens after tokens got unlocked. The Beneficiary can’t block tokens after the listing date.

The Beneficiary can’t withdraw capital collected during the block period.

The goal of this requirements is to ensure at no point in time the Beneficiary has custody over both tokens in a pair (they collected Token B and can block Token A withdrawal indefinitely). The protocol should guarantee for Users that they get Token A or can get the Token B back (as a refund).

⚠️ This feature is optional. There can be presales that allow Users to get Their Token A immediately.

### Vesting rights

The User want to be able to transfer their vesting rights to another wallet just as they can transfer an NFT or tokens.

### Fees

Dev Team can withdraw fees on terms equivalent to the Beneficiary’s withdrawal terms (refundable capital can’t be withdrawn).

Anyone can trigger fee withdrawal.

Capital gets transferred to a predefined address.

Fees must be withdrawn using a different function than the capital because in case either the Beneficiary or Fee Collector get’s blacklisted by TokenB, withdrawals should still be possible for the other party.

### Flexibility

Parameters of a presale and vesting can be updated by the Beneficiary until they mark the presale as locked or it’s less than an hour before the presale starts. At that point, no parameter can be updated.

This is to accommodate for real-life scenarios where plans change and presale parameters need to be updated. At the same time, Users can participate in the presale on a clear, locked in place rules.

Exceptions—what can be configured afterwards:

- whitelist root and proofs location

### Presale creation

A Beneficiary/Presale Manager can setup a new presale without deploying any smart contracts.

### Vesting schedule

Supported shapes:

- linear
  ```
        ／
      ／
    ／
  ／
  ```
- cliff linear
  ```
           ／
         ／
       ／
  ————
  ```
- steps (periodic)
  ```
        ——
       |
     ——
    |
  ——
  ```
- timelock
  ```
         ————
        |
        |
  ——————
  ```
- instant unlock, then linear
  ```
         ／
       ／
     ／
   ／
  |
  |
  ```
- instant unlock, cliff, then linear
  ```
            ／
          ／
        ／
   ————
  |
  |
  ```
  ```
          ／
        ／
       |
   ————
  |
  |
  ```
- instant unlock, cliff, then steps
  ```
              ——
             |
           ——
          |
   ——————
  |
  |
  ```

💡 Ideas for v2: exponential shape.

### Max token allocation

Max token allocation is defined per address. No need to define that as a smart contract state variable.

### Rounds

The Beneficiary wants to sell all tokens.

But the reality is not all whitelisted addresses will participate.

So the Beneficiary usually wants to setup a second round to sell tokens unsold in the first round.

What can differ between rounds:

- start timestamp
- end timestamp
- price
- whitelist (root hash and proofs)

The Beneficiary specifies rounds upfront.

There can be multiple rounds open at the same time.

### Unsold tokens

The Beneficiary can withdraw unsold Token A at any moment (before, during, and after any presale round).

### Price increase

Just as there can be various shapes for the vesting schedule, there could be a price action in each round. For example, there could be a round that lasts for 24h where every hour the price increases.

This is an idea for v2 and should not be implemented in v1.

### Access transferability (v2 idea, don’t implement)

Before the presale begins users can transfer their right to participate in the presale to another wallet.

For example, they might use a hardware wallet and might want to transfer their right to participate to a hot wallet to have an advantage during the time-sensitive FCFS (First-come, first-served) round.

Timeline:

- snapshot
- anyone can mint a Token P representing their right to participate in the presale
- anyone who has the Token P can transfer it to any wallet

Those who own the Token P:

- purchase Token A and the Token P gets burned

Those who didn’t mint the Token P:

- mint Token P, purchase Token A, Token P gets burned

The idea is Users can transfer/trade only the entire Token P unit (not fractions) but they can spend fractions when participating in the presale (as they don’t have to purchase max allocation, can purchase less).

When Users participate in presale, they burn a fraction or an entire Token P and get an equivalent amount of Token R.

Token R represents Users’ right for a refund.

In a scenario when a User has 1 Token P and purchases half of their allocation, and then another half, only one Token R should be minted to that user.

In a scenario when a User has 1 Token P and purchases half of their allocation, they get half of Token R minted.

These tokens can either be ERC-20 tokens or ERC-721 with properties designating their usage.

### Depositing FOO

Anyone can deposit Token A to the smart contract for presale. There is a special function for that but the logic should not depend entirely on the balanceOf because the token could theoretically be airdropped.

The Beneficiary can withdraw Token A at any time.

# Project setup

## Requirements

- [`foundry`](https://book.getfoundry.sh/)
- [`cargo`](https://doc.rust-lang.org/cargo/getting-started/installation.html)
- [`just`](https://github.com/casey/just#installation)
- [`echidna`](https://github.com/crytic/building-secure-contracts/blob/master/program-analysis/echidna/introduction/installation.md)
- [`slither`](https://github.com/crytic/slither#how-to-install)
- [`vertigo`](https://github.com/RareSkills/vertigo-rs)
- [`certora-cli`](https://docs.certora.com/en/latest/docs/user-guide/getting-started/install.html#step-2-install-the-certora-prover-package)

## Setup

```shell
just setup
```

## Tests

### Echidna tests

Every command run Echidna test suite in certain test mode property, overflow and assertion depending.

```shell
just invariant-test
```

```shell
just overflow-test
```

```shell
just assertion-test
```

### Types of tests we use in this project

- **Static analysis**: with static analysis, we’re detecting security vulnerabilities without running the code.
- **Example-based tests (aka concrete tests)**: This is your typical unit/integration/end-to-end tests. A concrete test tells a story: Alice did this, Bob did that, and x happened. Inputs and outputs are known. Tests are deterministic and reproducible. We don’t randomize inputs in these tests.
  - **Branching Tree Technique**: A technique that helps you to ensure your example-based tests cover all possible branches of business logic. Without it, you might have a tendency to only test a happy path. It prompts to think about all possible contract states and input variations and write more comprehensive test suite.  
    Here is a great introduction to BTT: https://prberg.com/presentations/spearbit-2023/  
    Examples: https://github.com/PaulRBerg/btt-examples/tree/main
- **Fuzzing**: The opposite of example-based tests—inputs are fully random. Not all inputs can be random in order to make sense. In these cases either:
  - eliminate some values with [`vm.assume`](https://book.getfoundry.sh/cheatcodes/assume)
  - provide a fixed value just like in an example-based test
- **Invariant testing**: In Foundry, Invariant testing applies Fuzzing across random function calls. With Fuzzing, you call a function or functions with fully-random inputs. With invariant testing, you call a set of functions in a random order with random inputs. The best resource to learn more is [RareSkills](https://www.rareskills.io/post/invariant-testing-solidity)
- **Property testing**: Is like Fuzzing but smarter. The test, instead of being a stupid monkey that throws fully random inputs, provides random inputs that meet certain criteria, eg. a number between 10 and 100. When writing these tests, we’re not trying to tell a story but rather validate [invariants](<https://en.wikipedia.org/wiki/Invariant_(mathematics)#Invariants_in_computer_science>)—properties that should always hold true.
- **Formal verification**: With Formal Verification we’re trying to create a mathematical proof for correctness of a program.
- **Mutation testing**: The goal of mutation testing is to detect which parts of the business logic are not covered with an existing test suite. If all your tests currently pass and they cover all logic branches of your business logic perfectly, any change to that business logic should fail. Mutation testing introduces _mutants_—changes to the Solidity code—and executes existing test suite to see if it fails.

### Testing tools we use in this project

- Static analysis: [**Slither**](https://github.com/crytic/slither)
- Example-based tests: [**Forge**](https://book.getfoundry.sh/forge/writing-tests)
- State-tree testing: [**Bulloak**](https://github.com/alexfertel/bulloak)
- Fuzzing: [**Forge**](https://book.getfoundry.sh/forge/fuzz-testing)
- Invariant testing: [**Forge**](https://book.getfoundry.sh/forge/invariant-testing)
- Property testing: [**Echidna**](https://github.com/crytic/echidna)
- Formal verification: [**Certora Prover**](https://docs.certora.com)
- Mutation testing: [**vertigo-rs**](https://github.com/RareSkills/vertigo-rs)

Resources:

- [Comprehensive overview of smart contract testing tools](https://www.rareskills.io/post/smart-contract-audit-tools)

### When to write which tests

We practice TDD (Test-Driven Development) which mean we write tests _before_ we write code.

But not all tests should be written before the implementation.

Here is a recommended test-implementation flow:

1. Gather requirements for the function you’re about to implement.
2. Write a function definition without a body
3. Scaffold an outline for the function with `just outline`
4. In a newly created `.outline` file, describe all possible execution paths by asking yourself:
   1. What contract state leads to which paths? Define `given state X` nodes
   2. What function params lead to which paths? Define `when parameter X` nodes
   3. Define `it should` leafs.
5. Scaffold example-based tests with `just tree`
6. Implement the function using a TDD cycle—each time first writing a failing test, then writing the implementation until it makes the test pass. Do that until you test all leafs described with the state tree.
7. Clean-up the implementation following the “make it work, make it right, make it fast” rule.
8. Run slither.
9. Now that you’re pretty confident with your implementation, throw a stupid monkey at it by writing forge fuzzing tests.
10. Write great NasSpec docs for the function.
11. Identify invariants and use invariant testing, property testing, and formal verification according to your own judgement.
12. Run `vertigo-rs` to check if your tests covered all logic branches.

Inspired by the [simple security toolkit](https://github.com/nascentxyz/simple-security-toolkit/blob/main/development-process.md#testing)

### Formal verification

<!-- TODO this section needs improvements -->

[Certora documentation](https://docs.certora.com/en/latest/index.html)

```shell
$ certoraRun certora/conf/CONTRACT_WHICH_YOU_NEED
```

**Before start work with Prover export your certora key as an environment variable**

```shell
$ export CERTORAKEY=<your_key>
```

### Static Analysis

`just static-analysis`

### Tokens

#### Decimals

To use tokens with non-standard decimals (not 18):

- tokenA - change the key `TEST_TOKEN_A_DECIMALS` in the `.env` file to the desired value
- tokenB - change the key `TEST_TOKEN_B_DECIMALS` in the `.env` file to the desired value

#### Existing tokens on the blockchain

To use existing tokens:

- tokenA - change the key `TEST_TOKEN_A_ADDRESS` in the `.env` file to the desired value
- tokenB - change the key `TEST_TOKEN_B_ADDRESS` in the `.env` file to the desired value

The test case uses the `deal` function to set the balance of a given token. The `deal` function should work for most ERC-20 tokens. If it doesn't work (such as USDC for forge-std version below 1.8.0), the variable `TEST_TOKEN_A_DONOR_ADDRESS` or `TEST_TOKEN_B_DONOR_ADDRESS` - address from which the funds will be transferred - should be declared.

Run `forge test --fork-url <rpc_url>`

#### Weird ERC20

List of "weird" ERC20 tokens: https://github.com/d-xo/weird-erc20

To use:

- tokenA - change the key `TEST_TOKEN_A_ADDRESS` in the `.env` file to the desired value
- tokenB - change the key `TEST_TOKEN_B_ADDRESS` in the `.env` file to the desired value

Run `forge test --fork-url <rpc_url>`

### Local anvil snapshots

To create a local snapshot: `cast rpc --rpc-url <rpc_url> evm_snapshot`

To revert the state to a snapshot: `cast rpc --rpc-url <rpc_url> evm_revert <snapshot_id>`

# Frontend integration guide

⚠️ This section contains pseudocode.

## Known data

We assume the frontend knows:

- the address of a Presale smart contract,
- a wallet address of a user connected with the dApp,
- the blockchain on which the presale takes place
- the symbol and decimals of Token A and B
- launchpad name (tglp or tpl)
- addresses of NFTs SaleMembership and vFOO (returnable and vested)

## The first render (wallet not connected)

The Frontend app has all information necessary to render an initial screen.

This assumes the initial screen does _not_ include:

- allocation size (each wallet can have different in each round)
- presale duration
- vesting schedule (each wallet can have different)

The frontend may prefetch information about rounds (`Presale.rounds()`) because that will be immediately needed after the wallet is connected.

Returned data includes:

- round name
- start timestamp
- end timestamp
- root hash
- proofs uri

Next, the frontend establishes which round is the current one and if there are any upcoming rounds. Note, that there may be more than one round open at the same time.

## After the wallet is connected

Immediately after the wallet gets connected, the dApp needs to know a few things. These differ depending on if the current state of presale rounds.

### For pending rounds (round hasn’t started yet)

- if the wallet is whitelisted for the pending round and on what terms (try fetching `${proofsURI}/${address}.json`. If you get 404, it’s not).
- allow whitelisted users to increase allowance

### For active rounds

- if the wallet is whitelisted for any of the current rounds and on what terms (try fetching `${proofsURI}/${address}.json`. If you get 404, it’s not).
- if the wallet participated in presale and therefore their proofs won’t be accepted anymore (proofs can only be used once): `Presale.participatedInPresale(roundId, address)`
- if the wallet owns any SaleMemberships (`SaleMembership.balanceOf(address)`). If yes, which ones? `SaleMembership.tokenOfOwnerByIndex(address, index)`
- which round do these SaleMemberships belong to? Filter out SaleMemberships that don’t belong to the current round (`SaleMembership.getAttributes(tokenId).roundId`)
- can the wallet buy more tokens with any of the SaleMemberships they own for the currently active round? `SaleMembership.getUsage(tokenId)` will return `{max, current}` and the frontend can render something like “Bought 30 out of 100 FOO available”.

For now we assume people will not transfer SaleMemberships so the frontend should only be concerned with one SaleMembership (multiple SaleMemberships in one wallet not supported) for a given round.

What it mean in practice, is this: imagine a scenario a user owns 2 SaleMemberships, both with current=30 and max=100. That means, the user can buy 140 FOO tokens (70 for each SaleMembership they own).

The frontend would have to display a list of SaleMemberships to the user, allow them to pick one, and allow them to purchase more tokens for that SaleMembership.

We won’t do any of that for now.

### For inactive rounds (round ended)

- if the wallet owns any SaleMemberships (`SaleMembership.balanceOf(address)`). If yes, which ones? `SaleMembership.tokenOfOwnerByIndex(address, index)`
- the same for vFOOs—do they own any and which ones?
- which round do these SaleMemberships and vFOOs belong to? Filter out those that don’t belong to the current (inactive) round (`SaleMembership.getAttributes(tokenId).roundId`)
- can any of the SaleMemberships still be refunded?
  - when does the possibility for refunds end? `Presale.listingTimestamp() + Presale.refundsPeriodDuration()`
  - can any tokens be refunded using that SaleMembership? If `SaleMembership.getUsage(tokenId).current == 0` then no—all tokens have either been refunded or claimed.
- can any of the SaleMemberships still be used to claim first tokens? If `SaleMembership.getUsage(tokenId).current == 0` then no—all tokens have either been refunded or claimed.
- If a wallet owns any vFOOs:
  - can any tokens be claimed? `vFOO.METHOD_TBD`
  - how many tokens have been claimed so far? `vFOO.getUsage(tokenId)`
  - and other metadata

Similarly to SaleMemberships, the first version of `vest-portal` assumes the user can have only one vFOO so no need for pickers from which vFOO the user wants to claim tokens, etc.

# DX (Developer Experience) TODO

- pre-commit git hook that checks if remappings.txt changed. If yes, it runs `npm run update-remappings` and adds changes to a commit.
- pre-commit git hook that runs `forge fmt` and adds changes to a commit
- pre-commit git hook that runs `forge snapshot` and adds results to a commit (this way we can track gas changes across commits). We might want to use [`forge-gas-snapshot`](https://github.com/marktoda/forge-gas-snapshot) for this.
- pre-commit git hook that runs `forge doc` and adds changes to a commit. MRs with outdated docs should fail checks.
- pre-commit git hook that runs `bulloak check`. MRs with outdated tests should fail. Can add a commit with an automatic bulloak fix but the developer should still implement the test case before the MR passes.
- CI/CD job that validates each commit:
  - is everything formatted correctly
  - in the Merge Request comment, render gas diff table
  - run all tests and check if everything is passing
  - in the MergeRequest comment, render code coverage and code coverage diff
- use [`just`](https://github.com/casey/just) to define and run project-specific commands. [Example justfile from Uniswap v4](https://github.com/Uniswap/v4-core/blob/main/justfile)
- configure [`solhint`](https://github.com/protofire/solhint)
  - require named keys and values in mappings
  - contract name must match the file name
  - internal functions at the bottom
  - don’t use require, throw errors instead
- pick and choose from https://github.com/ScopeLift/foundry-template

# TODO

## Towards internal v1

- [x] Refresh NFT design (https://gitlab.tenset.io/vest/vest-evm/-/issues/5)
- [x] Implement cliff height (https://gitlab.tenset.io/vest/vest-evm/-/issues/11)
- [x] Handle tokenB blacklisting addresses (withdrawTokenB) (https://gitlab.tenset.io/vest/vest-evm/-/issues/12)
  - [ ] try..catch
  - [ ] separate withdrawal methods
  - [ ] changing addresses (probably best)
- [x] Customizable refund period per allocation (https://gitlab.tenset.io/vest/vest-evm/-/issues/13)
  - Those with refund period zero should not be able to refund before listing
- [x] Customizable TGE_LISTING_DIFFERENCE (https://gitlab.tenset.io/vest/vest-evm/-/issues/9)
  - [ ] an ability to update during the presale
  - [ ] Update the way vesting schedule is calculated
    - currently it’s calculated since the token is unlocked, which is why we had to add 35 days cliff on ALVA
    - we want it to be calculated since the listing timestamp even if the token can be unlocked earlier
- [x] Block NFT trading before the listing (transfers still possible) (https://gitlab.tenset.io/vest/vest-evm/-/issues/14)
- [x] For some participants, block NFT trading forever (https://gitlab.tenset.io/vest/vest-evm/-/issues/14)
- [x] Run all tests with production deployment scripts (https://gitlab.tenset.io/vest/vest-evm/-/issues/15)
- [x] Use weird ERC20 for tests (https://gitlab.tenset.io/vest/vest-evm/-/issues/16)
- [x] Remove simple proxy to avoid getting marked as scam (https://gitlab.tenset.io/vest/vest-evm/-/issues/17)
- [ ] DX section from this README (https://gitlab.tenset.io/vest/vest-evm/-/issues/18)
- [x] local anvil snapshots for testing
- [x] One address, multiple tiers (https://gitlab.tenset.io/vest/vest-evm/-/issues/19)

### Later if needed

- [ ] Nuke refundable unit (unless we want to implement claimback fee?)
  - 100% -> 80% USDT
- [ ] Claimback tokens sharing (those who didn’t claim get a bonus)
- [ ] Incentivize early claims with % of LP tokens?
- [ ] Early claim bonus (those who claim early get additional tokenA - not linearly)
- [ ] An ability to merge and split NFTs as long as they have the same attributes
  - do we want to allow refunds after splits?
- [ ] An ability for influences to giveaway shares of their allocation
- [ ] Flash loans
  - we earn fees
  - market makers / arbitragers can equalize prices across DEXes

## Towards external v1

- [ ] Allow projects to put their logo on the NFT (svg inside an svg)
- [ ] Separate liquidity per rounds
- [ ] Rounds with end timestamp 0 last forever
- [ ] Implement tokenA fees (maybe?)
- [ ] Allow Vest devs to put custom descriptor, all others have to use the default one

## Won’t implement

- [ ] Support tokens with fee on transfer
  - If the token that’s launching implements transfer fees, we expect it to whitelist the presale protocol so that there are no fees during the presale. Fees on tokenB will not be supported.
- [ ] Remove enumerable
  - Enumerable increases `buy` costs by 25% but they simplify other aspects of the infrastructure. Removing enumerable would mean:
    - project have to spend money on off-chain indexers
    - in case the indexer goes down or returns incorrect data, a launch (which is a time sensitive event) can experience issues
    - the presale page requires a server component, which adds costs, complexity, maintenance overhead, etc.
  - Removing enumerable makes sense only on Ethereum, where gas costs are high. Meanwhile, covering that gas cost make economical sense for presale participants, given the potential profit from participation.
- No simple proxy
  - Simple proxy is a tradeoff between presale creation cost and participation cost. Removing the simple proxy means people will participate more cheaply while security scanners will not mark the presale smart contract as dangerous because it uses proxy.
