import { program } from '@commander-js/extra-typings';
import { glob } from 'glob';
import { read, write } from 'to-vfile';
import parser from '@solidity-parser/parser';
import type { ContractDefinition, FunctionDefinition } from '@solidity-parser/parser/dist/src/ast-types.d.ts';
import invariant from 'tiny-invariant';
import path from 'node:path';
import * as changeCase from 'change-case';
import fs from 'fs-extra';

function getContracts(ast: ReturnType<typeof parser.parse>) {
  const contractNodes: ContractDefinition[] = [];
  parser.visit(ast, {
    ContractDefinition: node => {
      if (['contract', 'library', 'abstract'].includes(node.kind)) contractNodes.push(node);
    },
  });
  return contractNodes;
}

function getPublicFunctions(contractNode: ContractDefinition) {
  const functionNodes: FunctionDefinition[] = [];
  parser.visit(contractNode, {
    FunctionDefinition: node => functionNodes.push(node),
  });
  return functionNodes.filter(node => {
    if (contractNode.kind === 'library') return true;
    return ['default', 'external', 'public'].includes(node.visibility);
  });
}

type Function = {
  filePath: string;
  contractName: string;
  functionName: string;
  outline: {
    path: string;
    value: string;
  };
};

function getOutlineFilePath({ filePath, contractName, functionName }: Pick<Function, 'filePath' | 'contractName' | 'functionName'>) {
  return path.join(
    'test',
    'concrete',
    path.dirname(filePath).split(path.sep).slice(1).join(path.delimiter),
    changeCase.kebabCase(path.basename(filePath).replace(path.extname(filePath), '')),
    changeCase.kebabCase(functionName),
    `${contractName}.${functionName}.outline`
  );
}

program.action(async () => {
  const solFiles = await glob('{src,test/mocks}/**/*.sol');
  if (solFiles.length === 0) {
    console.log('No Solidity files to process.');
  }
  const functions: Function[] = [];
  for (const filePath of solFiles) {
    const vFile = await read(filePath, 'utf-8');
    invariant(typeof vFile.value === 'string');
    const ast = parser.parse(vFile.value);
    const contractNodes = getContracts(ast);
    for (const contractNode of contractNodes) {
      const publicFunctions = getPublicFunctions(contractNode);
      publicFunctions.forEach(functionNode => {
        const functionName = (() => {
          if (functionNode.isConstructor) return 'constructor';
          if (functionNode.isFallback) return 'fallback';
          if (functionNode.isReceiveEther) return 'receive';
          return functionNode.name;
        })();
        const { name: contractName } = contractNode;
        functions.push({
          filePath,
          contractName,
          functionName,
          outline: { path: getOutlineFilePath({ filePath, contractName, functionName }), value: `${contractName}_${functionName}` },
        });
      });
    }
  }
  for (const publicFunction of functions) {
    if (!(await fs.exists(publicFunction.outline.path))) {
      await fs.ensureDir(path.dirname(publicFunction.outline.path));
      await fs.writeFile(publicFunction.outline.path, publicFunction.outline.value);
    }
  }
  const outlineFiles = await glob('test/concrete/**/*.outline');
  const outdatedOutlines = outlineFiles.filter(path => !functions.some(publicFunction => publicFunction.outline.path == path));
  await Promise.all(outdatedOutlines.map(outdatedOutline => fs.remove(path.dirname(outdatedOutline))));
});

(async () => {
  program.parseAsync();
})();
