name: Slither Analysis
on:
  push:
    branches:
      - master
      - release-v*
  pull_request: {}
  workflow_dispatch: {}

jobs:
  analyze:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 12.x
      - uses: actions/cache@v3
        id: cache
        with:
          path: '**/node_modules'
          key: npm-v2-${{ hashFiles('**/package-lock.json') }}
          restore-keys: npm-v2-
      - run: npm ci
        if: steps.cache.outputs.cache-hit != 'true'
      - name: Clean project         
        run: npm run clean
      - uses: crytic/slither-action@v0.1.1
