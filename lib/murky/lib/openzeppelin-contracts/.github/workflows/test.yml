name: Test

on:
  push:
    branches:
      - master
      - release-v*
  pull_request: {}
  workflow_dispatch: {}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 12.x
      - uses: actions/cache@v3
        id: cache
        with:
          path: '**/node_modules'
          key: npm-v2-${{ hashFiles('**/package-lock.json') }}
          restore-keys: npm-v2-
      - run: npm ci
        if: steps.cache.outputs.cache-hit != 'true'
      - run: npm run lint
      - run: npm run test
        env:
          FORCE_COLOR: 1
          ENABLE_GAS_REPORT: true
      - run: npm run test:inheritance
      - name: Print gas report
        run: cat gas-report.txt

  coverage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 2
      - uses: actions/setup-node@v3
        with:
          node-version: 12.x
      - uses: actions/cache@v3
        id: cache
        with:
          path: '**/node_modules'
          key: npm-v2-${{ hashFiles('**/package-lock.json') }}
          restore-keys: npm-v2-
      - run: npm ci
        if: steps.cache.outputs.cache-hit != 'true'
      - run: npm run coverage
        env:
          NODE_OPTIONS: --max_old_space_size=4096
      - uses: codecov/codecov-action@v3
