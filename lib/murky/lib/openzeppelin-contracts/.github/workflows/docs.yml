name: Build Docs

on:
  push:
    branches: [release-v*]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 12.x
      - uses: actions/cache@v3
        id: cache
        with:
          path: '**/node_modules'
          key: npm-v2-${{ hashFiles('**/package-lock.json') }}
          restore-keys: npm-v2-
      - run: npm ci
        if: steps.cache.outputs.cache-hit != 'true'
      - run: bash scripts/git-user-config.sh
      - run: node scripts/update-docs-branch.js
      - run: git push --all origin 
