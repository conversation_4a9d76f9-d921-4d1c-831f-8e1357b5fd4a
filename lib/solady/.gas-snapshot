Base64Test:testBase64DecodeSentenceGas() (gas: 3663)
Base64Test:testBase64DecodeShortStringGas() (gas: 919)
Base64Test:testBase64EncodeDecode(bytes) (runs: 256, μ: 13919, ~: 10697)
Base64Test:testBase64EncodeDecodeAltModes(bytes) (runs: 256, μ: 831731, ~: 763591)
Base64Test:testBase64EncodeEmptyString() (gas: 953)
Base64Test:testBase64EncodeFileSafeAndNoPadding(bytes,bool,bool) (runs: 256, μ: 16096, ~: 13634)
Base64Test:testBase64EncodeSentence() (gas: 4757)
Base64Test:testBase64EncodeShortStrings() (gas: 8496)
Base64Test:testBase64EncodeToStringWithDoublePadding() (gas: 1702)
Base64Test:testBase64EncodeToStringWithNoPadding() (gas: 1680)
Base64Test:testBase64EncodeToStringWithSinglePadding() (gas: 1636)
Base64Test:testBase64WordBoundary() (gas: 12511)
Base64Test:test__codesize() (gas: 7802)
CREATE3Test:testDeployERC20() (gas: 761926)
CREATE3Test:testDeployERC20(bytes32,string,string,uint8) (runs: 256, μ: 805442, ~: 808154)
CREATE3Test:testDeployedUpperBitsSafeForPlainSolidity() (gas: 657)
CREATE3Test:testDoubleDeployDifferentBytecodeReverts() (gas: 96899279)
CREATE3Test:testDoubleDeployDifferentBytecodeReverts(bytes32,bytes,bytes) (runs: 256, μ: 96880119, ~: 96880005)
CREATE3Test:testDoubleDeploySameBytecodeReverts() (gas: 96889159)
CREATE3Test:testDoubleDeploySameBytecodeReverts(bytes32,bytes) (runs: 256, μ: 96881043, ~: 96881007)
CREATE3Test:test__codesize() (gas: 15337)
DateTimeLibTest:testAddSubDiffDays(uint256,uint256) (runs: 256, μ: 4153, ~: 4166)
DateTimeLibTest:testAddSubDiffHours(uint256,uint256) (runs: 256, μ: 4102, ~: 4085)
DateTimeLibTest:testAddSubDiffMinutes(uint256,uint256) (runs: 256, μ: 4101, ~: 4074)
DateTimeLibTest:testAddSubDiffMonths(uint256,uint256) (runs: 256, μ: 7196, ~: 7187)
DateTimeLibTest:testAddSubDiffSeconds(uint256,uint256) (runs: 256, μ: 3742, ~: 3728)
DateTimeLibTest:testAddSubDiffYears(uint256,uint256) (runs: 256, μ: 6759, ~: 6752)
DateTimeLibTest:testDateTimeArithmeticReverts() (gas: 4595)
DateTimeLibTest:testDateTimeMaxSupported() (gas: 2881)
DateTimeLibTest:testDateTimeToAndFroTimestamp((uint256,uint256,uint256,uint256,uint256,uint256)) (runs: 256, μ: 4007, ~: 4090)
DateTimeLibTest:testDateToAndFroEpochDay((uint256,uint256,uint256,uint256,uint256,uint256)) (runs: 256, μ: 2465, ~: 2444)
DateTimeLibTest:testDateToAndFroEpochDay() (gas: 866488)
DateTimeLibTest:testDateToAndFroTimestamp() (gas: 908693)
DateTimeLibTest:testDateToEpochDay() (gas: 1603)
DateTimeLibTest:testDateToEpochDayDifferential((uint256,uint256,uint256,uint256,uint256,uint256)) (runs: 256, μ: 2203, ~: 2178)
DateTimeLibTest:testDateToEpochDayDifferential2((uint256,uint256,uint256,uint256,uint256,uint256)) (runs: 256, μ: 2126, ~: 2105)
DateTimeLibTest:testDateToEpochDayGas() (gas: 756479)
DateTimeLibTest:testDateToEpochDayGas2() (gas: 759345)
DateTimeLibTest:testDayOfWeek() (gas: 175297)
DateTimeLibTest:testDaysInMonth() (gas: 1204)
DateTimeLibTest:testDaysInMonth(uint256,uint256) (runs: 256, μ: 1035, ~: 1060)
DateTimeLibTest:testDaysToDate() (gas: 8377)
DateTimeLibTest:testEpochDayToDate(uint256) (runs: 256, μ: 1045, ~: 1045)
DateTimeLibTest:testEpochDayToDateDifferential(uint256) (runs: 256, μ: 1816, ~: 1766)
DateTimeLibTest:testEpochDayToDateDifferential2(uint256) (runs: 256, μ: 1754, ~: 1687)
DateTimeLibTest:testEpochDayToDateGas() (gas: 340747)
DateTimeLibTest:testEpochDayToDateGas2() (gas: 357444)
DateTimeLibTest:testIsLeapYear() (gas: 741)
DateTimeLibTest:testIsLeapYear(uint256) (runs: 256, μ: 522, ~: 495)
DateTimeLibTest:testIsSupportedDateFalse() (gas: 1651)
DateTimeLibTest:testIsSupportedDateTime((uint256,uint256,uint256,uint256,uint256,uint256)) (runs: 256, μ: 2834, ~: 2842)
DateTimeLibTest:testIsSupportedDateTrue() (gas: 670)
DateTimeLibTest:testIsSupportedEpochDayFalse() (gas: 597)
DateTimeLibTest:testIsSupportedEpochDayTrue() (gas: 305)
DateTimeLibTest:testIsSupportedTimestampFalse() (gas: 599)
DateTimeLibTest:testIsSupportedTimestampTrue() (gas: 304)
DateTimeLibTest:testIsWeekEnd(uint256) (runs: 256, μ: 727, ~: 662)
DateTimeLibTest:testMondayTimestamp() (gas: 1106)
DateTimeLibTest:testMondayTimestamp(uint256) (runs: 256, μ: 730, ~: 650)
DateTimeLibTest:testNthWeekdayInMonthOfYearTimestamp() (gas: 12031)
DateTimeLibTest:testNthWeekdayInMonthOfYearTimestamp(uint256,uint256,uint256,uint256) (runs: 256, μ: 3458, ~: 3467)
DateTimeLibTest:testWeekday() (gas: 705)
DateTimeLibTest:test__codesize() (gas: 20080)
DynamicBufferLibTest:testClear(uint256) (runs: 256, μ: 170998, ~: 171121)
DynamicBufferLibTest:testDynamicBuffer(bytes[],uint256) (runs: 256, μ: 989381, ~: 903737)
DynamicBufferLibTest:testDynamicBuffer(uint256) (runs: 256, μ: 956867, ~: 732551)
DynamicBufferLibTest:testDynamicBufferChaining() (gas: 22178)
DynamicBufferLibTest:testDynamicBufferReserveFromEmpty() (gas: 2781)
DynamicBufferLibTest:testDynamicBufferReserveFromEmpty2() (gas: 2423)
DynamicBufferLibTest:testDynamicBufferReserveFromEmpty3(bytes,uint256) (runs: 256, μ: 4100, ~: 2068)
DynamicBufferLibTest:testJoinWithConcat() (gas: 31407)
DynamicBufferLibTest:testJoinWithDynamicBuffer() (gas: 10764)
DynamicBufferLibTest:test__codesize() (gas: 11003)
ECDSATest:testBytes32ToEthSignedMessageHash() (gas: 381)
ECDSATest:testBytesToEthSignedMessageHash() (gas: 11581161)
ECDSATest:testBytesToEthSignedMessageHashEmpty() (gas: 556)
ECDSATest:testBytesToEthSignedMessageHashExceedsMaxLengthReverts() (gas: 606779)
ECDSATest:testBytesToEthSignedMessageHashLong() (gas: 681)
ECDSATest:testBytesToEthSignedMessageHashShort() (gas: 620)
ECDSATest:testEmptyCalldataHelpers() (gas: 3253)
ECDSATest:testRecoverAndTryRecover(bytes32) (runs: 256, μ: 2784471, ~: 2500743)
ECDSATest:testRecoverWithInvalidLongSignatureReverts() (gas: 7377)
ECDSATest:testRecoverWithInvalidShortSignatureReturnsZero() (gas: 7168)
ECDSATest:testRecoverWithInvalidSignatureReverts() (gas: 7955)
ECDSATest:testRecoverWithV0SignatureWithShortEIP2098Format() (gas: 4567)
ECDSATest:testRecoverWithV0SignatureWithShortEIP2098FormatAsCalldata() (gas: 8712)
ECDSATest:testRecoverWithV0SignatureWithVersion00Reverts() (gas: 7898)
ECDSATest:testRecoverWithV0SignatureWithVersion27() (gas: 8812)
ECDSATest:testRecoverWithV0SignatureWithWrongVersionReverts() (gas: 7876)
ECDSATest:testRecoverWithV1SignatureWithShortEIP2098Format() (gas: 4611)
ECDSATest:testRecoverWithV1SignatureWithShortEIP2098FormatAsCalldata() (gas: 8644)
ECDSATest:testRecoverWithV1SignatureWithVersion01Reverts() (gas: 7910)
ECDSATest:testRecoverWithV1SignatureWithVersion28() (gas: 8813)
ECDSATest:testRecoverWithV1SignatureWithWrongVersionReverts() (gas: 7877)
ECDSATest:testRecoverWithValidSignature() (gas: 8938)
ECDSATest:testRecoverWithWrongSigner() (gas: 8829)
ECDSATest:testTryRecoverWithInvalidLongSignatureReturnsZero() (gas: 5223)
ECDSATest:testTryRecoverWithInvalidShortSignatureReturnsZero() (gas: 5049)
ECDSATest:testTryRecoverWithInvalidSignature() (gas: 8924)
ECDSATest:testTryRecoverWithV0SignatureWithShortEIP2098Format() (gas: 4556)
ECDSATest:testTryRecoverWithV0SignatureWithShortEIP2098FormatAsCalldata() (gas: 8713)
ECDSATest:testTryRecoverWithV0SignatureWithVersion00ReturnsZero() (gas: 8809)
ECDSATest:testTryRecoverWithV0SignatureWithVersion27() (gas: 8832)
ECDSATest:testTryRecoverWithV0SignatureWithWrongVersionReturnsZero() (gas: 8832)
ECDSATest:testTryRecoverWithV1SignatureWithShortEIP2098Format() (gas: 4610)
ECDSATest:testTryRecoverWithV1SignatureWithShortEIP2098FormatAsCalldata() (gas: 8735)
ECDSATest:testTryRecoverWithV1SignatureWithVersion01ReturnsZero() (gas: 8853)
ECDSATest:testTryRecoverWithV1SignatureWithVersion28() (gas: 8832)
ECDSATest:testTryRecoverWithV1SignatureWithWrongVersionReturnsZero() (gas: 8831)
ECDSATest:testTryRecoverWithValidSignature() (gas: 8901)
ECDSATest:testTryRecoverWithWrongSigner() (gas: 8827)
ECDSATest:test__codesize() (gas: 15458)
EIP712Test:testDomainSeparator() (gas: 6026)
EIP712Test:testDomainSeparatorOnClone() (gas: 8842)
EIP712Test:testDomainSeparatorOnCloneDynamicWithChainIdChange() (gas: 69650)
EIP712Test:testDomainSeparatorOnCloneWithChainIdChange() (gas: 13334)
EIP712Test:testDomainSeparatorOnDynamicWithChainIdChange() (gas: 29288)
EIP712Test:testDomainSeparatorWithChainIdChange() (gas: 10391)
EIP712Test:testEIP5267() (gas: 35389)
EIP712Test:testHashTypedData() (gas: 37210)
EIP712Test:testHashTypedDataOnClone() (gas: 40216)
EIP712Test:testHashTypedDataOnCloneDynamic() (gas: 46957)
EIP712Test:testHashTypedDataOnCloneDynamicWithChaindIdChange() (gas: 58250)
EIP712Test:testHashTypedDataOnCloneWithChaindIdChange() (gas: 49558)
EIP712Test:testHashTypedDataOnDynamic() (gas: 44865)
EIP712Test:testHashTypedDataOnDynamicWithChaindIdChange() (gas: 57357)
EIP712Test:testHashTypedDataWithChaindIdChange() (gas: 45775)
EIP712Test:test__codesize() (gas: 13881)
ERC1155HooksTest:testERC1155Hooks() (gas: 4396277)
ERC1155HooksTest:test__codesize() (gas: 12073)
ERC1155Test:testApproveAll(address,bool) (runs: 256, μ: 50185, ~: 58576)
ERC1155Test:testAuthorizedEquivalence(address,address,bool) (runs: 256, μ: 715, ~: 715)
ERC1155Test:testBalanceOfBatchWithArrayMismatchReverts(uint256) (runs: 256, μ: 32581, ~: 34944)
ERC1155Test:testBatchBalanceOf(uint256) (runs: 256, μ: 105392, ~: 93414)
ERC1155Test:testBatchBurn(uint256) (runs: 256, μ: 173094, ~: 161352)
ERC1155Test:testBatchBurnInsufficientBalanceReverts(uint256) (runs: 256, μ: 168653, ~: 173551)
ERC1155Test:testBatchBurnWithArrayLengthMismatchReverts(uint256) (runs: 256, μ: 43154, ~: 42624)
ERC1155Test:testBatchMintToEOA(uint256) (runs: 256, μ: 119932, ~: 100124)
ERC1155Test:testBatchMintToERC1155Recipient(uint256) (runs: 256, μ: 777521, ~: 776273)
ERC1155Test:testBatchMintToNonERC1155RecipientReverts(uint256) (runs: 256, μ: 174065, ~: 185870)
ERC1155Test:testBatchMintToRevertingERC1155RecipientReverts(uint256) (runs: 256, μ: 329998, ~: 316370)
ERC1155Test:testBatchMintToWrongReturnDataERC1155RecipientReverts(uint256) (runs: 256, μ: 296789, ~: 318698)
ERC1155Test:testBatchMintToZeroReverts(uint256) (runs: 256, μ: 74829, ~: 65134)
ERC1155Test:testBatchMintWithArrayMismatchReverts(uint256) (runs: 256, μ: 33726, ~: 35514)
ERC1155Test:testBurn(uint256) (runs: 256, μ: 91141, ~: 82367)
ERC1155Test:testBurnInsufficientBalanceReverts(uint256) (runs: 256, μ: 97026, ~: 97439)
ERC1155Test:testDirectSetApprovalForAll(address,address,bool) (runs: 256, μ: 24436, ~: 15497)
ERC1155Test:testMintToEOA(uint256) (runs: 256, μ: 72151, ~: 71656)
ERC1155Test:testMintToERC1155Recipient(uint256) (runs: 256, μ: 671783, ~: 653495)
ERC1155Test:testMintToNonERC155RecipientReverts(uint256) (runs: 256, μ: 103267, ~: 103230)
ERC1155Test:testMintToRevertingERC155RecipientReverts(uint256) (runs: 256, μ: 281428, ~: 281562)
ERC1155Test:testMintToWrongReturnDataERC155RecipientReverts(uint256) (runs: 256, μ: 235948, ~: 235934)
ERC1155Test:testMintToZeroReverts(uint256) (runs: 256, μ: 33089, ~: 33062)
ERC1155Test:testSafeBatchTransfer() (gas: 8330001)
ERC1155Test:testSafeBatchTransferFromToEOA(uint256) (runs: 256, μ: 204636, ~: 189113)
ERC1155Test:testSafeBatchTransferFromToERC1155Recipient(uint256) (runs: 256, μ: 862741, ~: 884928)
ERC1155Test:testSafeBatchTransferFromToNonERC1155RecipientReverts(uint256) (runs: 256, μ: 261119, ~: 268265)
ERC1155Test:testSafeBatchTransferFromToRevertingERC1155RecipientReverts(uint256) (runs: 256, μ: 436886, ~: 447082)
ERC1155Test:testSafeBatchTransferFromToWrongReturnDataERC1155RecipientReverts(uint256) (runs: 256, μ: 340655, ~: 329939)
ERC1155Test:testSafeBatchTransferFromToZeroReverts(uint256) (runs: 256, μ: 139947, ~: 124620)
ERC1155Test:testSafeBatchTransferFromWithArrayLengthMismatchReverts(uint256) (runs: 256, μ: 51968, ~: 66732)
ERC1155Test:testSafeBatchTransferInsufficientBalanceReverts(uint256) (runs: 256, μ: 171512, ~: 173686)
ERC1155Test:testSafeTransferFromInsufficientBalanceReverts(uint256) (runs: 256, μ: 99102, ~: 99609)
ERC1155Test:testSafeTransferFromSelf(uint256) (runs: 256, μ: 106252, ~: 105774)
ERC1155Test:testSafeTransferFromSelfInsufficientBalanceReverts(uint256) (runs: 256, μ: 71420, ~: 72143)
ERC1155Test:testSafeTransferFromToEOA(uint256) (runs: 256, μ: 118610, ~: 111536)
ERC1155Test:testSafeTransferFromToERC1155Recipient(uint256) (runs: 256, μ: 759548, ~: 779513)
ERC1155Test:testSafeTransferFromToNonERC155RecipientReverts(uint256) (runs: 256, μ: 140334, ~: 139762)
ERC1155Test:testSafeTransferFromToRevertingERC1155RecipientReverts(uint256) (runs: 256, μ: 317222, ~: 318321)
ERC1155Test:testSafeTransferFromToWrongReturnDataERC1155RecipientReverts(uint256) (runs: 256, μ: 273149, ~: 272676)
ERC1155Test:testSafeTransferFromToZeroReverts(uint256) (runs: 256, μ: 70822, ~: 70216)
ERC1155Test:test__codesize() (gas: 43462)
ERC1967FactoryTest:testChangeAdmin() (gas: 266356)
ERC1967FactoryTest:testChangeAdminUnauthorized() (gas: 257316)
ERC1967FactoryTest:testDeploy() (gas: 257363)
ERC1967FactoryTest:testDeployAndCall(uint256) (runs: 256, μ: 339582, ~: 340919)
ERC1967FactoryTest:testDeployAndCallWithRevert() (gas: 211881)
ERC1967FactoryTest:testDeployBrutalized(uint256) (runs: 256, μ: 118449, ~: 44132)
ERC1967FactoryTest:testDeployDeterministicAndCall(uint256) (runs: 256, μ: 313675, ~: 350127)
ERC1967FactoryTest:testFactoryDeployment() (gas: 856479)
ERC1967FactoryTest:testProxyFails() (gas: 259019)
ERC1967FactoryTest:testProxySucceeds() (gas: 255707)
ERC1967FactoryTest:testUpgrade() (gas: 266792)
ERC1967FactoryTest:testUpgradeAndCall() (gas: 355404)
ERC1967FactoryTest:testUpgradeAndCallWithRevert() (gas: 265645)
ERC1967FactoryTest:testUpgradeUnauthorized() (gas: 270340)
ERC1967FactoryTest:testUpgradeWithCorruptedProxy() (gas: 263163)
ERC1967FactoryTest:test__codesize() (gas: 34003)
ERC20Invariants:invariantBalanceSum() (runs: 256, calls: 3840, reverts: 2345)
ERC20Invariants:test__codesize() (gas: 8050)
ERC20Test:testApprove() (gas: 35730)
ERC20Test:testApprove(address,uint256) (runs: 256, μ: 29937, ~: 31181)
ERC20Test:testBurn() (gas: 61920)
ERC20Test:testBurn(address,uint256,uint256) (runs: 256, μ: 60238, ~: 62469)
ERC20Test:testBurnInsufficientBalanceReverts(address,uint256,uint256) (runs: 256, μ: 55222, ~: 56454)
ERC20Test:testDirectSpendAllowance(uint256) (runs: 256, μ: 61605, ~: 61821)
ERC20Test:testDirectTransfer(uint256) (runs: 256, μ: 101084, ~: 111935)
ERC20Test:testInfiniteApproveTransferFrom() (gas: 89993)
ERC20Test:testMetadata() (gas: 17598)
ERC20Test:testMint() (gas: 58668)
ERC20Test:testMint(address,uint256) (runs: 256, μ: 56402, ~: 58890)
ERC20Test:testMintOverMaxUintReverts() (gas: 55753)
ERC20Test:testPermit() (gas: 89727)
ERC20Test:testPermit(uint256) (runs: 256, μ: 89648, ~: 89659)
ERC20Test:testPermitBadDeadlineReverts(uint256) (runs: 256, μ: 41399, ~: 41421)
ERC20Test:testPermitBadNonceReverts(uint256) (runs: 256, μ: 41856, ~: 41796)
ERC20Test:testPermitPastDeadlineReverts(uint256) (runs: 256, μ: 35987, ~: 35986)
ERC20Test:testPermitReplayReverts(uint256) (runs: 256, μ: 92286, ~: 92358)
ERC20Test:testTransfer() (gas: 65298)
ERC20Test:testTransfer(address,uint256) (runs: 256, μ: 63026, ~: 65514)
ERC20Test:testTransferFrom() (gas: 85633)
ERC20Test:testTransferFrom(address,address,address,uint256,uint256) (runs: 256, μ: 89300, ~: 94758)
ERC20Test:testTransferFromInsufficientAllowanceReverts() (gas: 81443)
ERC20Test:testTransferFromInsufficientAllowanceReverts(address,uint256,uint256) (runs: 256, μ: 81372, ~: 81983)
ERC20Test:testTransferFromInsufficientBalanceReverts() (gas: 61836)
ERC20Test:testTransferFromInsufficientBalanceReverts(address,uint256,uint256) (runs: 256, μ: 64824, ~: 62441)
ERC20Test:testTransferInsufficientBalanceReverts() (gas: 55919)
ERC20Test:testTransferInsufficientBalanceReverts(address,uint256,uint256) (runs: 256, μ: 55045, ~: 56424)
ERC20Test:test__codesize() (gas: 24081)
ERC2981Test:testRoyaltyOverflowCheckDifferential(uint256,uint256) (runs: 256, μ: 456, ~: 458)
ERC2981Test:testSetAndGetRoyaltyInfo(uint256) (runs: 256, μ: 107783, ~: 104859)
ERC2981Test:test__codesize() (gas: 8419)
ERC4337FactoryTest:testCreateAccountRepeatedDeployment() (gas: 149757)
ERC4337FactoryTest:testCreateAccountRepeatedDeployment(uint256) (runs: 256, μ: 171780, ~: 171754)
ERC4337FactoryTest:testDeployDeterministic(uint256) (runs: 256, μ: 134439, ~: 139901)
ERC4337FactoryTest:test__codesize() (gas: 13520)
ERC4337Test:testCdFallback() (gas: 443989)
ERC4337Test:testCdFallback2() (gas: 1140699)
ERC4337Test:testDelegateExecute() (gas: 369570)
ERC4337Test:testDelegateExecute(uint256) (runs: 256, μ: 355535, ~: 344555)
ERC4337Test:testDelegateExecuteRevertsIfOwnerSlotValueChanged() (gas: 319282)
ERC4337Test:testDepositFunctions() (gas: 502955)
ERC4337Test:testDirectStorage() (gas: 70413)
ERC4337Test:testDisableInitializerForImplementation() (gas: 1177324)
ERC4337Test:testETHReceived() (gas: 16584)
ERC4337Test:testExecute() (gas: 382786)
ERC4337Test:testExecuteBatch() (gas: 692605)
ERC4337Test:testExecuteBatch(uint256) (runs: 256, μ: 515659, ~: 368308)
ERC4337Test:testInitializer() (gas: 285192)
ERC4337Test:testIsValidSignature() (gas: 111663)
ERC4337Test:testIsValidSignaturePersonalSign() (gas: 96270)
ERC4337Test:testIsValidSignatureWrapped() (gas: 406706)
ERC4337Test:testOnERC1155BatchReceived() (gas: 1393788)
ERC4337Test:testOnERC1155Received() (gas: 1391111)
ERC4337Test:testOnERC721Received() (gas: 1364600)
ERC4337Test:testOwnerRecovery() (gas: 486105)
ERC4337Test:testValidateUserOp() (gas: 491555)
ERC4337Test:test__codesize() (gas: 54768)
ERC4626Test:testDepositWithNoApprovalReverts() (gas: 16371)
ERC4626Test:testDepositWithNotEnoughApprovalReverts() (gas: 89884)
ERC4626Test:testDifferentialFullMulDiv(uint256,uint256,uint256) (runs: 256, μ: 3322, ~: 3185)
ERC4626Test:testMetadata() (gas: 15439)
ERC4626Test:testMintWithNoApprovalReverts() (gas: 16345)
ERC4626Test:testMintZero() (gas: 54317)
ERC4626Test:testMultipleMintDepositRedeemWithdraw() (gas: 425481)
ERC4626Test:testRedeemWithNoShareAmountReverts() (gas: 10918)
ERC4626Test:testRedeemWithNotEnoughShareAmountReverts() (gas: 142915)
ERC4626Test:testSingleDepositWithdraw(uint128) (runs: 256, μ: 202651, ~: 202662)
ERC4626Test:testSingleMintRedeem(uint128) (runs: 256, μ: 201579, ~: 201589)
ERC4626Test:testTryGetAssetDecimals() (gas: 30466616)
ERC4626Test:testUseVirtualShares() (gas: 2439368)
ERC4626Test:testVaultInteractionsForSomeoneElse() (gas: 296164)
ERC4626Test:testVirtualSharesMultipleMintDepositRedeemWithdraw() (gas: 1636698)
ERC4626Test:testWithdrawWithNoUnderlyingAmountReverts() (gas: 13102)
ERC4626Test:testWithdrawWithNotEnoughUnderlyingAmountReverts() (gas: 144074)
ERC4626Test:testWithdrawZero() (gas: 52807)
ERC4626Test:test__codesize() (gas: 41067)
ERC6551Test:testCdFallback() (gas: 894585)
ERC6551Test:testDeployERC6551(uint256) (runs: 256, μ: 171507, ~: 168767)
ERC6551Test:testDeployERC6551Proxy() (gas: 80751)
ERC6551Test:testExecute() (gas: 507949)
ERC6551Test:testExecuteBatch() (gas: 817049)
ERC6551Test:testExecuteBatch(uint256) (runs: 256, μ: 614474, ~: 483214)
ERC6551Test:testInitializeERC6551ProxyImplementation() (gas: 189807)
ERC6551Test:testIsValidSignature() (gas: 187653)
ERC6551Test:testOnERC1155BatchReceived() (gas: 1526548)
ERC6551Test:testOnERC1155Received() (gas: 1523904)
ERC6551Test:testOnERC721Received() (gas: 1501328)
ERC6551Test:testOnERC721ReceivedCycles() (gas: 1714290)
ERC6551Test:testOnERC721ReceivedCyclesWithDifferentChainIds(uint256) (runs: 256, μ: 448897, ~: 454355)
ERC6551Test:testSupportsInterface() (gas: 169393)
ERC6551Test:testUpgrade() (gas: 1154917)
ERC6551Test:test__codesize() (gas: 48028)
ERC6909Test:testApprove() (gas: 36771)
ERC6909Test:testApprove(address,uint256,uint256) (runs: 256, μ: 36402, ~: 37413)
ERC6909Test:testBurn() (gas: 40676)
ERC6909Test:testBurn(address,uint256,uint256,uint256) (runs: 256, μ: 39910, ~: 41187)
ERC6909Test:testBurnInsufficientBalanceReverts(address,uint256,uint256,uint256) (runs: 256, μ: 34114, ~: 35036)
ERC6909Test:testDecimals() (gas: 5676)
ERC6909Test:testDirectApprove() (gas: 36733)
ERC6909Test:testDirectFunctions(uint256) (runs: 256, μ: 199312, ~: 201336)
ERC6909Test:testDirectSetOperator() (gas: 35937)
ERC6909Test:testDirectTransfer() (gas: 65833)
ERC6909Test:testInfiniteApproveTransferFrom() (gas: 78194)
ERC6909Test:testMetadata(uint256) (runs: 256, μ: 9332, ~: 9332)
ERC6909Test:testMint() (gas: 36938)
ERC6909Test:testMint(address,uint256,uint256) (runs: 256, μ: 36241, ~: 37174)
ERC6909Test:testMintOverMaxUintReverts() (gas: 34388)
ERC6909Test:testMintOverMaxUintReverts(address,uint256,uint256,uint256) (runs: 256, μ: 35296, ~: 35348)
ERC6909Test:testOperatorTransferFrom() (gas: 73821)
ERC6909Test:testSetOperator() (gas: 37042)
ERC6909Test:testSetOperator(address,address,bool) (runs: 256, μ: 25706, ~: 16767)
ERC6909Test:testTokenURI() (gas: 33117)
ERC6909Test:testTransfer() (gas: 48107)
ERC6909Test:testTransfer(address,uint256,uint256) (runs: 256, μ: 46179, ~: 47218)
ERC6909Test:testTransferFrom() (gas: 74360)
ERC6909Test:testTransferFrom(address,address,address,uint256,uint256,uint256) (runs: 256, μ: 77014, ~: 80776)
ERC6909Test:testTransferFromCallerIsNotOperator(address,uint256,uint256) (runs: 256, μ: 39318, ~: 39315)
ERC6909Test:testTransferFromInsufficientAllowanceReverts(address,uint256,uint256,uint256) (runs: 256, μ: 66048, ~: 67379)
ERC6909Test:testTransferFromInsufficientBalanceReverts() (gas: 49548)
ERC6909Test:testTransferFromInsufficientBalanceReverts(address,uint256,uint256,uint256) (runs: 256, μ: 52046, ~: 49776)
ERC6909Test:testTransferFromInsufficientPermission() (gas: 66854)
ERC6909Test:testTransferFromOverMaxUintReverts() (gas: 89947)
ERC6909Test:testTransferFromOverMaxUintReverts(address,uint256,uint256,uint256) (runs: 256, μ: 112429, ~: 112948)
ERC6909Test:testTransferInsufficientBalanceReverts() (gas: 34534)
ERC6909Test:testTransferInsufficientBalanceReverts(address,uint256,uint256,uint256) (runs: 256, μ: 34681, ~: 35076)
ERC6909Test:testTransferOverMaxUintReverts() (gas: 63438)
ERC6909Test:testTransferOverMaxUintReverts(address,uint256,uint256,uint256) (runs: 256, μ: 63960, ~: 63973)
ERC6909Test:test__codesize() (gas: 26802)
ERC721HooksTest:testERC721Hooks() (gas: 2936628)
ERC721HooksTest:test__codesize() (gas: 9992)
ERC721Test:testApprove(uint256) (runs: 256, μ: 108104, ~: 108140)
ERC721Test:testApproveAll(uint256) (runs: 256, μ: 47589, ~: 40334)
ERC721Test:testApproveBurn(uint256) (runs: 256, μ: 86780, ~: 86840)
ERC721Test:testApproveNonExistentReverts(uint256,address) (runs: 256, μ: 33639, ~: 33571)
ERC721Test:testApproveUnauthorizedReverts(uint256) (runs: 256, μ: 83292, ~: 82427)
ERC721Test:testAuthorizedEquivalence(address,bool,bool) (runs: 256, μ: 748, ~: 743)
ERC721Test:testAux(uint256) (runs: 256, μ: 191888, ~: 193041)
ERC721Test:testBurn(uint256) (runs: 256, μ: 82657, ~: 93940)
ERC721Test:testBurnNonExistentReverts(uint256) (runs: 256, μ: 10783, ~: 10783)
ERC721Test:testCannotExceedMaxBalance() (gas: 169055)
ERC721Test:testDoubleBurnReverts(uint256) (runs: 256, μ: 63579, ~: 63525)
ERC721Test:testDoubleMintReverts(uint256) (runs: 256, μ: 79140, ~: 79170)
ERC721Test:testEverything(uint256) (runs: 256, μ: 307083, ~: 301998)
ERC721Test:testExtraData(uint256) (runs: 256, μ: 99074, ~: 99124)
ERC721Test:testExtraData2(uint256,uint256) (runs: 256, μ: 54285, ~: 53934)
ERC721Test:testIsApprovedOrOwner(uint256) (runs: 256, μ: 135171, ~: 135190)
ERC721Test:testMint(uint256) (runs: 256, μ: 82831, ~: 82871)
ERC721Test:testMintAndSetExtraDataUnchecked(uint256) (runs: 256, μ: 84355, ~: 84391)
ERC721Test:testMintAndSetExtraDataUncheckedWithOverwrite(uint256,uint96) (runs: 256, μ: 83606, ~: 83515)
ERC721Test:testMintToZeroReverts(uint256) (runs: 256, μ: 39197, ~: 39197)
ERC721Test:testOwnerOfNonExistent(uint256) (runs: 256, μ: 33401, ~: 33338)
ERC721Test:testSafeMintToEOA(uint256) (runs: 256, μ: 83347, ~: 83388)
ERC721Test:testSafeMintToERC721Recipient(uint256) (runs: 256, μ: 409323, ~: 410424)
ERC721Test:testSafeMintToERC721RecipientWithData(uint256,bytes) (runs: 256, μ: 470739, ~: 460016)
ERC721Test:testSafeMintToERC721RecipientWithWrongReturnData(uint256) (runs: 256, μ: 169926, ~: 169926)
ERC721Test:testSafeMintToERC721RecipientWithWrongReturnDataWithData(uint256,bytes) (runs: 256, μ: 171141, ~: 171088)
ERC721Test:testSafeMintToNonERC721RecipientReverts(uint256) (runs: 256, μ: 100388, ~: 100388)
ERC721Test:testSafeMintToNonERC721RecipientWithDataReverts(uint256,bytes) (runs: 256, μ: 101635, ~: 101582)
ERC721Test:testSafeMintToRevertingERC721RecipientReverts(uint256) (runs: 256, μ: 203045, ~: 203045)
ERC721Test:testSafeMintToRevertingERC721RecipientWithDataReverts(uint256,bytes) (runs: 256, μ: 204281, ~: 204228)
ERC721Test:testSafeTransferFromToEOA(uint256) (runs: 256, μ: 121886, ~: 121998)
ERC721Test:testSafeTransferFromToERC721Recipient(uint256) (runs: 256, μ: 470903, ~: 472029)
ERC721Test:testSafeTransferFromToERC721RecipientWithData(uint256,bytes) (runs: 256, μ: 532326, ~: 521625)
ERC721Test:testSafeTransferFromToERC721RecipientWithWrongReturnDataReverts(uint256) (runs: 256, μ: 200873, ~: 200927)
ERC721Test:testSafeTransferFromToERC721RecipientWithWrongReturnDataWithDataReverts(uint256,bytes) (runs: 256, μ: 202114, ~: 202125)
ERC721Test:testSafeTransferFromToNonERC721RecipientReverts(uint256) (runs: 256, μ: 131279, ~: 131217)
ERC721Test:testSafeTransferFromToNonERC721RecipientWithDataReverts(uint256,bytes) (runs: 256, μ: 132582, ~: 132592)
ERC721Test:testSafeTransferFromToRevertingERC721RecipientReverts(uint256) (runs: 256, μ: 233944, ~: 233996)
ERC721Test:testSafeTransferFromToRevertingERC721RecipientWithDataReverts(uint256,bytes) (runs: 256, μ: 235248, ~: 235263)
ERC721Test:testSafetyOfCustomStorage(uint256,uint256) (runs: 256, μ: 1063, ~: 713)
ERC721Test:testTransferFrom() (gas: 85763)
ERC721Test:testTransferFrom(uint256) (runs: 256, μ: 113890, ~: 112456)
ERC721Test:testTransferFromApproveAll(uint256) (runs: 256, μ: 119295, ~: 119264)
ERC721Test:testTransferFromNotExistentReverts(address,address,uint256) (runs: 256, μ: 34033, ~: 34007)
ERC721Test:testTransferFromNotOwner(uint256) (runs: 256, μ: 84713, ~: 84702)
ERC721Test:testTransferFromSelf(uint256) (runs: 256, μ: 92706, ~: 92688)
ERC721Test:testTransferFromToZeroReverts(uint256) (runs: 256, μ: 62789, ~: 62784)
ERC721Test:testTransferFromWrongFromReverts(address,uint256) (runs: 256, μ: 80429, ~: 80402)
ERC721Test:test__codesize() (gas: 43679)
FixedPointMathLibTest:testAbs() (gas: 578)
FixedPointMathLibTest:testAbs(int256) (runs: 256, μ: 516, ~: 485)
FixedPointMathLibTest:testAbsEdgeCases() (gas: 410)
FixedPointMathLibTest:testAvg() (gas: 420)
FixedPointMathLibTest:testAvgEdgeCase() (gas: 492)
FixedPointMathLibTest:testAvgSigned() (gas: 864)
FixedPointMathLibTest:testCbrt() (gas: 10306)
FixedPointMathLibTest:testCbrt(uint256) (runs: 256, μ: 1424, ~: 1513)
FixedPointMathLibTest:testCbrtBack(uint256) (runs: 256, μ: 32374, ~: 41603)
FixedPointMathLibTest:testCbrtWad() (gas: 11984)
FixedPointMathLibTest:testCbrtWad(uint256) (runs: 256, μ: 1996, ~: 2005)
FixedPointMathLibTest:testClamp(uint256,uint256,uint256) (runs: 256, μ: 546, ~: 546)
FixedPointMathLibTest:testClampSigned(int256,int256,int256) (runs: 256, μ: 610, ~: 611)
FixedPointMathLibTest:testDist() (gas: 634)
FixedPointMathLibTest:testDist(int256,int256) (runs: 256, μ: 519, ~: 519)
FixedPointMathLibTest:testDistEdgeCases() (gas: 570)
FixedPointMathLibTest:testDivWad() (gas: 769)
FixedPointMathLibTest:testDivWad(uint256,uint256) (runs: 256, μ: 794, ~: 913)
FixedPointMathLibTest:testDivWadEdgeCases() (gas: 436)
FixedPointMathLibTest:testDivWadOverflowReverts(uint256,uint256) (runs: 256, μ: 3749, ~: 3749)
FixedPointMathLibTest:testDivWadUp() (gas: 3085)
FixedPointMathLibTest:testDivWadUp(uint256,uint256) (runs: 256, μ: 856, ~: 1009)
FixedPointMathLibTest:testDivWadUpEdgeCases() (gas: 483)
FixedPointMathLibTest:testDivWadUpOverflowReverts(uint256,uint256) (runs: 256, μ: 3748, ~: 3748)
FixedPointMathLibTest:testDivWadUpZeroDenominatorReverts() (gas: 3239)
FixedPointMathLibTest:testDivWadUpZeroDenominatorReverts(uint256) (runs: 256, μ: 3273, ~: 3273)
FixedPointMathLibTest:testDivWadZeroDenominatorReverts() (gas: 3240)
FixedPointMathLibTest:testDivWadZeroDenominatorReverts(uint256) (runs: 256, μ: 3315, ~: 3315)
FixedPointMathLibTest:testExpWad() (gas: 8047)
FixedPointMathLibTest:testFactorial() (gas: 98825)
FixedPointMathLibTest:testFactorialOriginal() (gas: 94187)
FixedPointMathLibTest:testFullMulDiv() (gas: 1140)
FixedPointMathLibTest:testFullMulDiv(uint256,uint256,uint256) (runs: 256, μ: 1398, ~: 1031)
FixedPointMathLibTest:testFullMulDivUp(uint256,uint256,uint256) (runs: 256, μ: 1719, ~: 1523)
FixedPointMathLibTest:testFullMulDivUpRevertsIfRoundedUpResultOverflowsCase1() (gas: 3677)
FixedPointMathLibTest:testFullMulDivUpRevertsIfRoundedUpResultOverflowsCase2() (gas: 3688)
FixedPointMathLibTest:testGcd() (gas: 4206)
FixedPointMathLibTest:testGcd(uint256,uint256) (runs: 256, μ: 5005, ~: 2223)
FixedPointMathLibTest:testLambertW0WadAccuracy() (gas: 7170)
FixedPointMathLibTest:testLambertW0WadAccuracy(uint184) (runs: 256, μ: 1687, ~: 386)
FixedPointMathLibTest:testLambertW0WadKnownValues() (gas: 1689664)
FixedPointMathLibTest:testLambertW0WadMonoDebug() (gas: 772621)
FixedPointMathLibTest:testLambertW0WadMonotonicallyIncreasing() (gas: 18485651)
FixedPointMathLibTest:testLambertW0WadMonotonicallyIncreasing(int256,int256) (runs: 256, μ: 7085, ~: 6969)
FixedPointMathLibTest:testLambertW0WadMonotonicallyIncreasing2() (gas: 4045078)
FixedPointMathLibTest:testLambertW0WadMonotonicallyIncreasingAround(int256) (runs: 256, μ: 26569, ~: 17524)
FixedPointMathLibTest:testLambertW0WadMonotonicallyIncreasingAround2(uint96) (runs: 256, μ: 56296, ~: 17524)
FixedPointMathLibTest:testLambertW0WadRevertsForOutOfDomain() (gas: 16612)
FixedPointMathLibTest:testLambertW0WadWithinBounds() (gas: 122475)
FixedPointMathLibTest:testLambertW0WadWithinBounds(int256) (runs: 256, μ: 3168, ~: 2628)
FixedPointMathLibTest:testLnWad() (gas: 2054)
FixedPointMathLibTest:testLnWadBig() (gas: 2087)
FixedPointMathLibTest:testLnWadNegativeReverts() (gas: 3318)
FixedPointMathLibTest:testLnWadOverflowReverts() (gas: 3293)
FixedPointMathLibTest:testLnWadSmall() (gas: 2644)
FixedPointMathLibTest:testLog10() (gas: 76168)
FixedPointMathLibTest:testLog10(uint256,uint256) (runs: 256, μ: 2154, ~: 2215)
FixedPointMathLibTest:testLog10Up() (gas: 4413)
FixedPointMathLibTest:testLog2() (gas: 243209)
FixedPointMathLibTest:testLog256() (gas: 22831)
FixedPointMathLibTest:testLog256(uint256,uint256) (runs: 256, μ: 2030, ~: 2110)
FixedPointMathLibTest:testLog256Up() (gas: 1249)
FixedPointMathLibTest:testLog2Differential(uint256) (runs: 256, μ: 871, ~: 864)
FixedPointMathLibTest:testLog2Up() (gas: 297368)
FixedPointMathLibTest:testMax(uint256,uint256) (runs: 256, μ: 517, ~: 513)
FixedPointMathLibTest:testMaxCasted(uint32,uint32,uint256) (runs: 256, μ: 865, ~: 870)
FixedPointMathLibTest:testMaxSigned(int256,int256) (runs: 256, μ: 542, ~: 542)
FixedPointMathLibTest:testMin(uint256,uint256) (runs: 256, μ: 499, ~: 504)
FixedPointMathLibTest:testMinBrutalized(uint256,uint256) (runs: 256, μ: 807, ~: 817)
FixedPointMathLibTest:testMinSigned(int256,int256) (runs: 256, μ: 541, ~: 541)
FixedPointMathLibTest:testMulDiv() (gas: 1890)
FixedPointMathLibTest:testMulDiv(uint256,uint256,uint256) (runs: 256, μ: 760, ~: 858)
FixedPointMathLibTest:testMulDivEdgeCases() (gas: 784)
FixedPointMathLibTest:testMulDivOverflowReverts(uint256,uint256,uint256) (runs: 256, μ: 3832, ~: 3832)
FixedPointMathLibTest:testMulDivUp() (gas: 2103)
FixedPointMathLibTest:testMulDivUp(uint256,uint256,uint256) (runs: 256, μ: 909, ~: 1121)
FixedPointMathLibTest:testMulDivUpEdgeCases() (gas: 817)
FixedPointMathLibTest:testMulDivUpOverflowReverts(uint256,uint256,uint256) (runs: 256, μ: 3877, ~: 3877)
FixedPointMathLibTest:testMulDivUpZeroDenominator() (gas: 3231)
FixedPointMathLibTest:testMulDivUpZeroDenominatorReverts(uint256,uint256) (runs: 256, μ: 3310, ~: 3310)
FixedPointMathLibTest:testMulDivZeroDenominatorReverts() (gas: 3253)
FixedPointMathLibTest:testMulDivZeroDenominatorReverts(uint256,uint256) (runs: 256, μ: 3377, ~: 3377)
FixedPointMathLibTest:testMulWad() (gas: 714)
FixedPointMathLibTest:testMulWad(uint256,uint256) (runs: 256, μ: 724, ~: 855)
FixedPointMathLibTest:testMulWadEdgeCases() (gas: 715)
FixedPointMathLibTest:testMulWadOverflowReverts(uint256,uint256) (runs: 256, μ: 3832, ~: 3832)
FixedPointMathLibTest:testMulWadUp() (gas: 815)
FixedPointMathLibTest:testMulWadUp(uint256,uint256) (runs: 256, μ: 833, ~: 1043)
FixedPointMathLibTest:testMulWadUpEdgeCases() (gas: 793)
FixedPointMathLibTest:testMulWadUpOverflowReverts(uint256,uint256) (runs: 256, μ: 3769, ~: 3769)
FixedPointMathLibTest:testPackUnpackSci() (gas: 129327)
FixedPointMathLibTest:testPackUnpackSci(uint256) (runs: 256, μ: 30139, ~: 30088)
FixedPointMathLibTest:testRPow() (gas: 3276)
FixedPointMathLibTest:testRPowOverflowReverts() (gas: 4951)
FixedPointMathLibTest:testRawAdd(int256,int256) (runs: 256, μ: 462, ~: 462)
FixedPointMathLibTest:testRawAdd(uint256,uint256) (runs: 256, μ: 485, ~: 485)
FixedPointMathLibTest:testRawAddMod(uint256,uint256,uint256) (runs: 256, μ: 577, ~: 577)
FixedPointMathLibTest:testRawDiv(uint256,uint256) (runs: 256, μ: 443, ~: 443)
FixedPointMathLibTest:testRawMod(uint256,uint256) (runs: 256, μ: 464, ~: 464)
FixedPointMathLibTest:testRawMul(int256,int256) (runs: 256, μ: 412, ~: 412)
FixedPointMathLibTest:testRawMul(uint256,uint256) (runs: 256, μ: 485, ~: 485)
FixedPointMathLibTest:testRawMulMod(uint256,uint256,uint256) (runs: 256, μ: 579, ~: 579)
FixedPointMathLibTest:testRawSDiv(int256,int256) (runs: 256, μ: 487, ~: 487)
FixedPointMathLibTest:testRawSMod(int256,int256) (runs: 256, μ: 488, ~: 488)
FixedPointMathLibTest:testRawSub(int256,int256) (runs: 256, μ: 463, ~: 463)
FixedPointMathLibTest:testRawSub(uint256,uint256) (runs: 256, μ: 418, ~: 418)
FixedPointMathLibTest:testSDivWad() (gas: 894)
FixedPointMathLibTest:testSDivWad(int256,int256) (runs: 256, μ: 842, ~: 971)
FixedPointMathLibTest:testSDivWadEdgeCases() (gas: 422)
FixedPointMathLibTest:testSDivWadOverflowReverts(int256,int256) (runs: 256, μ: 3803, ~: 3803)
FixedPointMathLibTest:testSDivWadZeroDenominatorReverts(int256) (runs: 256, μ: 3307, ~: 3307)
FixedPointMathLibTest:testSMulWad() (gas: 1010)
FixedPointMathLibTest:testSMulWad(int256,int256) (runs: 256, μ: 865, ~: 1048)
FixedPointMathLibTest:testSMulWadEdgeCases() (gas: 1309)
FixedPointMathLibTest:testSMulWadOverflowRevertsOnCondition1(int256,int256) (runs: 256, μ: 3842, ~: 3842)
FixedPointMathLibTest:testSMulWadOverflowRevertsOnCondition2(int256) (runs: 256, μ: 3751, ~: 3751)
FixedPointMathLibTest:testSMulWadOverflowTrickDifferential(int256,int256) (runs: 256, μ: 620, ~: 632)
FixedPointMathLibTest:testSci() (gas: 1838636)
FixedPointMathLibTest:testSci(uint256) (runs: 256, μ: 31708, ~: 36377)
FixedPointMathLibTest:testSci2(uint256) (runs: 256, μ: 902, ~: 911)
FixedPointMathLibTest:testSqrt() (gas: 42576)
FixedPointMathLibTest:testSqrt(uint256) (runs: 256, μ: 1015, ~: 1064)
FixedPointMathLibTest:testSqrtBack(uint256) (runs: 256, μ: 14589, ~: 363)
FixedPointMathLibTest:testSqrtHashed(uint256) (runs: 256, μ: 53167, ~: 53549)
FixedPointMathLibTest:testSqrtHashedSingle() (gas: 53041)
FixedPointMathLibTest:testSqrtWad() (gas: 7470)
FixedPointMathLibTest:testSqrtWad(uint256) (runs: 256, μ: 1560, ~: 1565)
FixedPointMathLibTest:testZeroFloorSub(uint256,uint256) (runs: 256, μ: 571, ~: 601)
FixedPointMathLibTest:testZeroFloorSubCasted(uint32,uint32,uint256) (runs: 256, μ: 922, ~: 886)
FixedPointMathLibTest:test__codesize() (gas: 42329)
GasBurnerLibTest:testBurnGas() (gas: 1700805)
GasBurnerLibTest:test__codesize() (gas: 1435)
InitializableTest:testDisableInitializers() (gas: 42058)
InitializableTest:testInitializableConstructor() (gas: 691167)
InitializableTest:testInitialize() (gas: 54712)
InitializableTest:testInitializeInititalizerTrick(bool,uint64,uint16) (runs: 256, μ: 791, ~: 791)
InitializableTest:testInitializeReinititalize(uint256) (runs: 256, μ: 93744, ~: 91284)
InitializableTest:testInitializeReinititalizerTrick(bool,uint64,uint64) (runs: 256, μ: 674, ~: 687)
InitializableTest:testOnlyInitializing() (gas: 10420)
InitializableTest:test__codesize() (gas: 11970)
JSONParserLibTest:testDecodeEncodedStringDoesNotRevert(string) (runs: 256, μ: 59104, ~: 57912)
JSONParserLibTest:testDecodeInvalidStringReverts() (gas: 178467)
JSONParserLibTest:testDecodeString() (gas: 206866)
JSONParserLibTest:testEmptyItem() (gas: 3715)
JSONParserLibTest:testParseEmptyArrays() (gas: 591658)
JSONParserLibTest:testParseEmptyObjects() (gas: 592619)
JSONParserLibTest:testParseGas() (gas: 151932)
JSONParserLibTest:testParseInt() (gas: 210763)
JSONParserLibTest:testParseInt(int256) (runs: 256, μ: 19061, ~: 14830)
JSONParserLibTest:testParseIntReverts(uint256) (runs: 256, μ: 56967, ~: 56948)
JSONParserLibTest:testParseIntTrick(uint256,bool) (runs: 256, μ: 556, ~: 554)
JSONParserLibTest:testParseInvalidIntReverts() (gas: 217257)
JSONParserLibTest:testParseInvalidNumberReverts() (gas: 4131682)
JSONParserLibTest:testParseInvalidReverts() (gas: 8689942)
JSONParserLibTest:testParseInvalidStringReverts() (gas: 2157455)
JSONParserLibTest:testParseInvalidUintFromHexReverts() (gas: 91030)
JSONParserLibTest:testParseInvalidUintReverts() (gas: 316454)
JSONParserLibTest:testParseJWTGas() (gas: 52743)
JSONParserLibTest:testParseNumber() (gas: 786927)
JSONParserLibTest:testParseObject() (gas: 49060)
JSONParserLibTest:testParseRecursiveObject() (gas: 104916)
JSONParserLibTest:testParseSimpleArray() (gas: 27225)
JSONParserLibTest:testParseSimpleUintArray() (gas: 1227137)
JSONParserLibTest:testParseSpecials() (gas: 295849)
JSONParserLibTest:testParseString() (gas: 1448516)
JSONParserLibTest:testParseUint() (gas: 99188)
JSONParserLibTest:testParseUint(uint256) (runs: 256, μ: 37656, ~: 31606)
JSONParserLibTest:testParseUintFromHex() (gas: 1374286)
JSONParserLibTest:testParseValidObjectDoesNotRevert(string,string) (runs: 256, μ: 50337, ~: 49281)
JSONParserLibTest:test__codesize() (gas: 39586)
LibBitTest:testAnd() (gas: 185887)
LibBitTest:testAnd(bool,bool) (runs: 256, μ: 625, ~: 627)
LibBitTest:testAutoClean(uint256,uint256) (runs: 256, μ: 446, ~: 446)
LibBitTest:testBoolToUint(bool) (runs: 256, μ: 524, ~: 524)
LibBitTest:testCLZ() (gas: 306404)
LibBitTest:testFFS() (gas: 181293)
LibBitTest:testFLS() (gas: 254381)
LibBitTest:testIsPo2() (gas: 65211)
LibBitTest:testIsPo2(uint256) (runs: 256, μ: 4982, ~: 1522)
LibBitTest:testIsPo2(uint8,uint8) (runs: 256, μ: 629, ~: 631)
LibBitTest:testOr() (gas: 188733)
LibBitTest:testOr(bool,bool) (runs: 256, μ: 620, ~: 619)
LibBitTest:testPassInBool() (gas: 824)
LibBitTest:testPopCount() (gas: 63738)
LibBitTest:testPopCount(uint256) (runs: 256, μ: 5124, ~: 1664)
LibBitTest:testReturnsBool() (gas: 779)
LibBitTest:testReverseBits() (gas: 131521)
LibBitTest:testReverseBitsDifferential(uint256) (runs: 256, μ: 18724, ~: 18724)
LibBitTest:testReverseBytes() (gas: 12492)
LibBitTest:testReverseBytesDifferential(uint256) (runs: 256, μ: 2675, ~: 2675)
LibBitTest:test__codesize() (gas: 6702)
LibBitmapTest:testBitmapClaimWithGetSet() (gas: 27089)
LibBitmapTest:testBitmapClaimWithToggle() (gas: 17479)
LibBitmapTest:testBitmapFindLastSet() (gas: 1300541)
LibBitmapTest:testBitmapFindLastSet(uint256,uint256) (runs: 256, μ: 76010, ~: 76159)
LibBitmapTest:testBitmapGet() (gas: 2565)
LibBitmapTest:testBitmapGet(uint256) (runs: 256, μ: 2593, ~: 2593)
LibBitmapTest:testBitmapPopCount() (gas: 750426)
LibBitmapTest:testBitmapPopCount(uint256,uint256,uint256) (runs: 256, μ: 200742, ~: 170994)
LibBitmapTest:testBitmapPopCountAcrossMultipleBuckets() (gas: 73611)
LibBitmapTest:testBitmapPopCountWithinSingleBucket() (gas: 34032)
LibBitmapTest:testBitmapSet() (gas: 22527)
LibBitmapTest:testBitmapSet(uint256) (runs: 256, μ: 22599, ~: 22599)
LibBitmapTest:testBitmapSetAndGet(uint256) (runs: 256, μ: 22633, ~: 22633)
LibBitmapTest:testBitmapSetBatch() (gas: 3147640)
LibBitmapTest:testBitmapSetBatchAcrossMultipleBuckets() (gas: 452545)
LibBitmapTest:testBitmapSetBatchWithinSingleBucket() (gas: 417119)
LibBitmapTest:testBitmapSetTo() (gas: 14275)
LibBitmapTest:testBitmapSetTo(uint256,bool,uint256) (runs: 256, μ: 13050, ~: 22774)
LibBitmapTest:testBitmapSetTo(uint256,uint256) (runs: 256, μ: 44937, ~: 49941)
LibBitmapTest:testBitmapToggle() (gas: 30810)
LibBitmapTest:testBitmapToggle(uint256,bool) (runs: 256, μ: 17742, ~: 14247)
LibBitmapTest:testBitmapUnset() (gas: 22572)
LibBitmapTest:testBitmapUnset(uint256) (runs: 256, μ: 14343, ~: 14363)
LibBitmapTest:testBitmapUnsetBatch() (gas: 3086142)
LibBitmapTest:testBitmapUnsetBatchAcrossMultipleBuckets() (gas: 453362)
LibBitmapTest:testBitmapUnsetBatchWithinSingleBucket() (gas: 446762)
LibBitmapTest:test__codesize() (gas: 8132)
LibCloneTest:testClone() (gas: 73078)
LibCloneTest:testClone(uint256) (runs: 256, μ: 73128, ~: 73128)
LibCloneTest:testCloneDeteministicWithImmutableArgs() (gas: 193238)
LibCloneTest:testCloneDeteministicWithImmutableArgs(address,uint256,uint256[],bytes,uint64,uint8,uint256) (runs: 256, μ: 1055886, ~: 996607)
LibCloneTest:testCloneDeterministic() (gas: 96877)
LibCloneTest:testCloneDeterministic(uint256,bytes32) (runs: 256, μ: 96924, ~: 96924)
LibCloneTest:testCloneDeterministicRevertsIfAddressAlreadyUsed() (gas: 96882835)
LibCloneTest:testCloneWithImmutableArgs() (gas: 120936)
LibCloneTest:testCloneWithImmutableArgs(uint256,address,uint256,uint256[],uint64,uint8) (runs: 256, μ: 987682, ~: 1012402)
LibCloneTest:testCloneWithImmutableArgsRevertsIfDataTooBig() (gas: 97305794)
LibCloneTest:testCreateDeterministicERC1967(uint256,bytes32) (runs: 256, μ: 125203, ~: 125203)
LibCloneTest:testCreateDeterministicERC1967I(uint256,bytes32) (runs: 256, μ: 129487, ~: 129487)
LibCloneTest:testDeployDeterministicERC1967() (gas: 123221)
LibCloneTest:testDeployDeterministicERC1967(uint256,bytes32) (runs: 256, μ: 123315, ~: 123315)
LibCloneTest:testDeployDeterministicERC1967I() (gas: 127581)
LibCloneTest:testDeployDeterministicERC1967I(uint256,bytes32) (runs: 256, μ: 127652, ~: 127652)
LibCloneTest:testDeployERC1967() (gas: 99367)
LibCloneTest:testDeployERC1967(uint256) (runs: 256, μ: 99418, ~: 99418)
LibCloneTest:testDeployERC1967CodeHashAndLength(address) (runs: 256, μ: 133562, ~: 134029)
LibCloneTest:testDeployERC1967I() (gas: 103641)
LibCloneTest:testDeployERC1967I(uint256) (runs: 256, μ: 103712, ~: 103712)
LibCloneTest:testDeployERC1967ICodeHashAndLength(address) (runs: 256, μ: 142001, ~: 142468)
LibCloneTest:testDeployERC1967ISpecialPath() (gas: 71720)
LibCloneTest:testDeployERC1967ISpecialPath(address,bytes1) (runs: 256, μ: 72155, ~: 72233)
LibCloneTest:testInitCode(address,uint256,uint256) (runs: 256, μ: 901256, ~: 880889)
LibCloneTest:testInitialDeposit() (gas: 323928)
LibCloneTest:testStartsWith(uint256) (runs: 256, μ: 29047, ~: 29052)
LibCloneTest:test__codesize() (gas: 22917)
LibMapTest:testFoundStatementDifferential(uint256,uint256,uint256) (runs: 256, μ: 499, ~: 499)
LibMapTest:testGeneralMapFunctionsGas() (gas: 3304775)
LibMapTest:testGeneralMapFunctionsWithSmallBitWidths(uint256) (runs: 256, μ: 81591, ~: 85939)
LibMapTest:testGeneralMapFunctionsWithZeroBitWidth() (gas: 12521)
LibMapTest:testGeneralMapSearchSorted(uint256) (runs: 256, μ: 183423, ~: 125787)
LibMapTest:testMapGetFromBigArray() (gas: 3308)
LibMapTest:testMapGetUint8() (gas: 3335)
LibMapTest:testMapSetUint8() (gas: 23321)
LibMapTest:testMapSetUint8FromBigArray() (gas: 23264)
LibMapTest:testUint128MapSearchSorted(uint256) (runs: 256, μ: 190273, ~: 131306)
LibMapTest:testUint128MapSetAndGet() (gas: 1607245)
LibMapTest:testUint128MapSetAndGet(uint256) (runs: 256, μ: 171538, ~: 169085)
LibMapTest:testUint128MapSetAndGet2(uint256) (runs: 256, μ: 68556, ~: 69274)
LibMapTest:testUint16MapSearchSorted(uint256) (runs: 256, μ: 91041, ~: 86498)
LibMapTest:testUint16MapSetAndGet() (gas: 958193)
LibMapTest:testUint16MapSetAndGet(uint256) (runs: 256, μ: 81137, ~: 81211)
LibMapTest:testUint16MapSetAndGet2(uint256) (runs: 256, μ: 66712, ~: 69185)
LibMapTest:testUint32MapSearchSorted(uint256) (runs: 256, μ: 95538, ~: 86079)
LibMapTest:testUint32MapSetAndGet() (gas: 1067093)
LibMapTest:testUint32MapSetAndGet(uint256) (runs: 256, μ: 113412, ~: 124319)
LibMapTest:testUint32MapSetAndGet2(uint256) (runs: 256, μ: 67498, ~: 69139)
LibMapTest:testUint32Maps(uint256) (runs: 256, μ: 65137, ~: 72265)
LibMapTest:testUint40MapSearchSorted(uint256) (runs: 256, μ: 135722, ~: 107899)
LibMapTest:testUint40MapSetAndGet() (gas: 1127344)
LibMapTest:testUint40MapSetAndGet(uint256) (runs: 256, μ: 135832, ~: 145996)
LibMapTest:testUint40MapSetAndGet2(uint256) (runs: 256, μ: 67028, ~: 69222)
LibMapTest:testUint64MapSearchSorted(uint256) (runs: 256, μ: 127136, ~: 108449)
LibMapTest:testUint64MapSetAndGet() (gas: 1220437)
LibMapTest:testUint64MapSetAndGet(uint256) (runs: 256, μ: 141740, ~: 146721)
LibMapTest:testUint64MapSetAndGet2(uint256) (runs: 256, μ: 65549, ~: 69189)
LibMapTest:testUint8MapSearchSorted(uint256) (runs: 256, μ: 97600, ~: 85977)
LibMapTest:testUint8MapSetAndGet() (gas: 883454)
LibMapTest:testUint8MapSetAndGet(uint256) (runs: 256, μ: 59152, ~: 59103)
LibMapTest:testUint8MapSetAndGet2(uint256) (runs: 256, μ: 67845, ~: 69101)
LibMapTest:test__codesize() (gas: 13187)
LibPRNGTest:testLCGGas() (gas: 20802)
LibPRNGTest:testPRNGGas() (gas: 25645)
LibPRNGTest:testPRNGNext() (gas: 16162)
LibPRNGTest:testPRNGShuffle() (gas: 504485)
LibPRNGTest:testPRNGShuffleBytes() (gas: 222095)
LibPRNGTest:testPRNGShuffleBytesGas() (gas: 1322452)
LibPRNGTest:testPRNGShuffleGas() (gas: 1610949)
LibPRNGTest:testPRNGUniform() (gas: 559341)
LibPRNGTest:testStandardNormalWad() (gas: 4254566)
LibPRNGTest:test__codesize() (gas: 5879)
LibRLPTest:testComputeAddressDifferential(address,uint256) (runs: 256, μ: 1913, ~: 1633)
LibRLPTest:testComputeAddressForLargeNonces() (gas: 1771)
LibRLPTest:testComputeAddressForSmallNonces() (gas: 967)
LibRLPTest:testComputeAddressOriginalForLargeNonces() (gas: 1887)
LibRLPTest:testComputeAddressOriginalForSmallNonces() (gas: 1477)
LibRLPTest:test__codesize() (gas: 8108)
LibSortTest:testCopy(uint256) (runs: 256, μ: 339370, ~: 25944)
LibSortTest:testInsertionSortAddressesDifferential(uint256) (runs: 256, μ: 34852, ~: 25750)
LibSortTest:testInsertionSortInts() (gas: 112135)
LibSortTest:testInsertionSortPsuedorandom() (gas: 62806)
LibSortTest:testInsertionSortPsuedorandom(uint256) (runs: 256, μ: 61937, ~: 61728)
LibSortTest:testIsSortedAddressesDifferential(uint256) (runs: 256, μ: 43410, ~: 29649)
LibSortTest:testIsSortedAndUniquifiedAddressesDifferential(uint256) (runs: 256, μ: 35123, ~: 29480)
LibSortTest:testIsSortedAndUniquifiedDifferential(uint256) (runs: 256, μ: 34978, ~: 26806)
LibSortTest:testIsSortedAndUniquifiedIntsDifferential(uint256) (runs: 256, μ: 91009, ~: 32013)
LibSortTest:testIsSortedDifferential(uint256) (runs: 256, μ: 38738, ~: 27521)
LibSortTest:testIsSortedIntsDifferential(uint256) (runs: 256, μ: 42529, ~: 30764)
LibSortTest:testReverse() (gas: 114573)
LibSortTest:testSearchSorted() (gas: 118972)
LibSortTest:testSearchSortedAddresses() (gas: 254850)
LibSortTest:testSearchSortedBasicCases() (gas: 2244)
LibSortTest:testSearchSortedDifferential(uint256) (runs: 256, μ: 48791, ~: 29238)
LibSortTest:testSearchSortedEdgeCases() (gas: 1675)
LibSortTest:testSearchSortedElementInArray(uint256) (runs: 256, μ: 52443, ~: 31684)
LibSortTest:testSearchSortedElementNotInArray() (gas: 6361)
LibSortTest:testSearchSortedElementNotInArray(uint256) (runs: 256, μ: 66497, ~: 33941)
LibSortTest:testSearchSortedInts() (gas: 120794)
LibSortTest:testSearchSortedInts(int256[],int256) (runs: 256, μ: 11895, ~: 12100)
LibSortTest:testSearchSortedWithEmptyArray() (gas: 738)
LibSortTest:testSort(uint256) (runs: 256, μ: 48249, ~: 29841)
LibSortTest:testSortAddressesDifferential(uint256) (runs: 256, μ: 101717, ~: 29625)
LibSortTest:testSortAddressesPsuedorandom() (gas: 144831)
LibSortTest:testSortAddressesPsuedorandom(uint256) (runs: 256, μ: 146525, ~: 146554)
LibSortTest:testSortAddressesPsuedorandomBrutalizeUpperBits() (gas: 195855)
LibSortTest:testSortAddressesReversed() (gas: 45400)
LibSortTest:testSortAddressesSorted() (gas: 42376)
LibSortTest:testSortBasicCase() (gas: 1210)
LibSortTest:testSortChecksumed(uint256) (runs: 256, μ: 46128, ~: 28317)
LibSortTest:testSortDifferential(uint256) (runs: 256, μ: 66739, ~: 27194)
LibSortTest:testSortInts() (gas: 612263)
LibSortTest:testSortMostlySame() (gas: 67374)
LibSortTest:testSortOriginalMostlySame() (gas: 207985)
LibSortTest:testSortOriginalPsuedorandom() (gas: 284071)
LibSortTest:testSortOriginalPsuedorandom(uint256) (runs: 256, μ: 285060, ~: 285780)
LibSortTest:testSortOriginalReversed() (gas: 156282)
LibSortTest:testSortOriginalSorted() (gas: 142263)
LibSortTest:testSortPsuedorandom() (gas: 138551)
LibSortTest:testSortPsuedorandom(uint256) (runs: 256, μ: 140145, ~: 140426)
LibSortTest:testSortPsuedorandomNonuniform() (gas: 145689)
LibSortTest:testSortPsuedorandomNonuniform(uint256) (runs: 256, μ: 145180, ~: 145471)
LibSortTest:testSortReversed() (gas: 38850)
LibSortTest:testSortSorted() (gas: 35785)
LibSortTest:testSortTestOverhead() (gas: 102190)
LibSortTest:testSortedDifferenceDifferential() (gas: 173992)
LibSortTest:testSortedDifferenceDifferential(uint256) (runs: 256, μ: 35776, ~: 35951)
LibSortTest:testSortedDifferenceDifferentialInt(uint256) (runs: 256, μ: 35462, ~: 34647)
LibSortTest:testSortedDifferenceUnionIntersection(uint256) (runs: 256, μ: 58250, ~: 52962)
LibSortTest:testSortedIntersectionDifferential() (gas: 208149)
LibSortTest:testSortedIntersectionDifferential(uint256) (runs: 256, μ: 31536, ~: 30632)
LibSortTest:testSortedIntersectionDifferentialInt(uint256) (runs: 256, μ: 36056, ~: 35170)
LibSortTest:testSortedUnionDifferential() (gas: 212603)
LibSortTest:testSortedUnionDifferential(uint256) (runs: 256, μ: 34018, ~: 33253)
LibSortTest:testSortedUnionDifferentialInt(uint256) (runs: 256, μ: 38526, ~: 39118)
LibSortTest:testTwoComplementConversionSort(int256,int256) (runs: 256, μ: 515, ~: 517)
LibSortTest:testUniquifySorted() (gas: 1688)
LibSortTest:testUniquifySorted(uint256) (runs: 256, μ: 45107, ~: 25165)
LibSortTest:testUniquifySortedAddress() (gas: 4017)
LibSortTest:testUniquifySortedAddress(uint256) (runs: 256, μ: 68095, ~: 28753)
LibSortTest:testUniquifySortedDifferential(uint256) (runs: 256, μ: 69117, ~: 33215)
LibSortTest:testUniquifySortedWithEmptyArray() (gas: 506)
LibSortTest:test__codesize() (gas: 23828)
LibStringTest:testAddressToHexStringZeroRightPadded(address) (runs: 256, μ: 3392, ~: 3392)
LibStringTest:testBytesToHexString() (gas: 6446)
LibStringTest:testBytesToHexString(bytes) (runs: 256, μ: 719427, ~: 611812)
LibStringTest:testBytesToHexStringNoPrefix() (gas: 6064)
LibStringTest:testBytesToHexStringNoPrefix(bytes) (runs: 256, μ: 795973, ~: 612177)
LibStringTest:testContains() (gas: 43922)
LibStringTest:testFromAddressToHexString() (gas: 3774)
LibStringTest:testFromAddressToHexStringChecksummed() (gas: 40426)
LibStringTest:testFromAddressToHexStringChecksummedDifferential(uint256) (runs: 256, μ: 734115, ~: 601006)
LibStringTest:testFromAddressToHexStringWithLeadingZeros() (gas: 3750)
LibStringTest:testHexStringNoPrefixVariants(uint256,uint256) (runs: 256, μ: 691936, ~: 611578)
LibStringTest:testNormalizeSmallString() (gas: 1365)
LibStringTest:testNormalizeSmallString(bytes32) (runs: 256, μ: 3122, ~: 4191)
LibStringTest:testStringConcat() (gas: 7385)
LibStringTest:testStringConcat(string,string) (runs: 256, μ: 668173, ~: 592737)
LibStringTest:testStringConcatOriginal() (gas: 8067)
LibStringTest:testStringDirectReturn() (gas: 8257)
LibStringTest:testStringDirectReturn(string) (runs: 256, μ: 3614, ~: 3455)
LibStringTest:testStringEndsWith() (gas: 2807)
LibStringTest:testStringEndsWith(uint256) (runs: 256, μ: 708014, ~: 625158)
LibStringTest:testStringEq(string,string) (runs: 256, μ: 1586, ~: 1587)
LibStringTest:testStringEqs() (gas: 2158)
LibStringTest:testStringEscapeHTML() (gas: 11903)
LibStringTest:testStringEscapeHTML(uint256) (runs: 256, μ: 770421, ~: 640514)
LibStringTest:testStringEscapeJSON() (gas: 53305)
LibStringTest:testStringEscapeJSONHexEncode() (gas: 728907)
LibStringTest:testStringIndexOf() (gas: 17420)
LibStringTest:testStringIndexOf(uint256) (runs: 256, μ: 708585, ~: 636508)
LibStringTest:testStringIndicesOf() (gas: 11680)
LibStringTest:testStringIndicesOf(uint256) (runs: 256, μ: 752738, ~: 638383)
LibStringTest:testStringIs7BitASCII() (gas: 205168)
LibStringTest:testStringIs7BitASCIIDifferential(bytes) (runs: 256, μ: 690084, ~: 591402)
LibStringTest:testStringLastIndexOf() (gas: 24020)
LibStringTest:testStringLastIndexOf(uint256) (runs: 256, μ: 679253, ~: 632413)
LibStringTest:testStringLowerDifferential() (gas: 3405778)
LibStringTest:testStringLowerDifferential(string) (runs: 256, μ: 8929, ~: 8560)
LibStringTest:testStringLowerOriginal() (gas: 1783)
LibStringTest:testStringPackAndUnpackOne() (gas: 744364)
LibStringTest:testStringPackAndUnpackOne(string) (runs: 256, μ: 667121, ~: 591167)
LibStringTest:testStringPackAndUnpackOneDifferential(string) (runs: 256, μ: 756941, ~: 590519)
LibStringTest:testStringPackAndUnpackTwo() (gas: 907346)
LibStringTest:testStringPackAndUnpackTwo(string,string) (runs: 256, μ: 675166, ~: 592886)
LibStringTest:testStringPackAndUnpackTwoDifferential(string,string) (runs: 256, μ: 701062, ~: 591576)
LibStringTest:testStringRepeat() (gas: 8998)
LibStringTest:testStringRepeat(string,uint256) (runs: 256, μ: 695813, ~: 594520)
LibStringTest:testStringRepeatOriginal() (gas: 13671)
LibStringTest:testStringReplace(uint256) (runs: 256, μ: 670306, ~: 641829)
LibStringTest:testStringReplaceLong() (gas: 9799)
LibStringTest:testStringReplaceMedium() (gas: 8541)
LibStringTest:testStringReplaceShort() (gas: 17434)
LibStringTest:testStringRuneCount() (gas: 2963738)
LibStringTest:testStringRuneCountDifferential(string) (runs: 256, μ: 6085, ~: 5862)
LibStringTest:testStringSlice() (gas: 17745)
LibStringTest:testStringSlice(uint256) (runs: 256, μ: 670024, ~: 637454)
LibStringTest:testStringSplit() (gas: 20265)
LibStringTest:testStringSplit(uint256) (runs: 256, μ: 716019, ~: 634153)
LibStringTest:testStringStartsWith() (gas: 2567)
LibStringTest:testStringStartsWith(uint256) (runs: 256, μ: 705074, ~: 623679)
LibStringTest:testStringUpperDifferential() (gas: 3819281)
LibStringTest:testStringUpperDifferential(string) (runs: 256, μ: 8929, ~: 8560)
LibStringTest:testStringUpperOriginal() (gas: 1781)
LibStringTest:testToHexStringFixedLengthInsufficientLength() (gas: 3395)
LibStringTest:testToHexStringFixedLengthPositiveNumberLong() (gas: 4454)
LibStringTest:testToHexStringFixedLengthPositiveNumberShort() (gas: 1502)
LibStringTest:testToHexStringFixedLengthUint256Max() (gas: 4500)
LibStringTest:testToHexStringFixedLengthZeroRightPadded(uint256,uint256) (runs: 256, μ: 8223, ~: 4866)
LibStringTest:testToHexStringPositiveNumber() (gas: 1407)
LibStringTest:testToHexStringUint256Max() (gas: 4219)
LibStringTest:testToHexStringZero() (gas: 1345)
LibStringTest:testToHexStringZeroRightPadded(uint256) (runs: 256, μ: 2084, ~: 1344)
LibStringTest:testToMinimalHexStringNoPrefixPositiveNumber() (gas: 6000)
LibStringTest:testToMinimalHexStringNoPrefixUint256Max() (gas: 4064)
LibStringTest:testToMinimalHexStringNoPrefixZero() (gas: 1350)
LibStringTest:testToMinimalHexStringNoPrefixZeroRightPadded(uint256) (runs: 256, μ: 2075, ~: 1343)
LibStringTest:testToMinimalHexStringPositiveNumber() (gas: 6133)
LibStringTest:testToMinimalHexStringUint256Max() (gas: 4247)
LibStringTest:testToMinimalHexStringZero() (gas: 1393)
LibStringTest:testToMinimalHexStringZeroRightPadded(uint256) (runs: 256, μ: 2109, ~: 1369)
LibStringTest:testToSmallString() (gas: 4426)
LibStringTest:testToStringPositiveNumber() (gas: 1470)
LibStringTest:testToStringPositiveNumberBrutalized() (gas: 1103731)
LibStringTest:testToStringSignedDifferential(int256) (runs: 256, μ: 671386, ~: 591457)
LibStringTest:testToStringSignedGas() (gas: 7283)
LibStringTest:testToStringSignedMemory(int256) (runs: 256, μ: 734251, ~: 590822)
LibStringTest:testToStringSignedOriginalGas() (gas: 9861)
LibStringTest:testToStringUint256Max() (gas: 7442)
LibStringTest:testToStringUint256MaxBrutalized() (gas: 603788)
LibStringTest:testToStringZero() (gas: 1196)
LibStringTest:testToStringZeroBrutalized() (gas: 591165)
LibStringTest:testToStringZeroRightPadded(uint256) (runs: 256, μ: 733933, ~: 591557)
LibStringTest:test__codesize() (gas: 42685)
LibZipTest:testCdCompress() (gas: 156007)
LibZipTest:testCdCompressDecompress(bytes) (runs: 256, μ: 772347, ~: 647360)
LibZipTest:testCdCompressDecompress(uint256) (runs: 256, μ: 786963, ~: 694314)
LibZipTest:testCdDecompressOnInvalidInput() (gas: 26080)
LibZipTest:testCdFallback() (gas: 5674556)
LibZipTest:testCdFallback(bytes,uint256) (runs: 256, μ: 1170023, ~: 1037541)
LibZipTest:testCdFallbackDecompressor(bytes) (runs: 256, μ: 121300, ~: 117570)
LibZipTest:testCdFallbackDecompressor(uint256) (runs: 256, μ: 170276, ~: 155248)
LibZipTest:testCdFallbackMaskTrick(uint256,uint256) (runs: 256, μ: 687, ~: 663)
LibZipTest:testDecompressWontRevert(bytes) (runs: 256, μ: 743387, ~: 624873)
LibZipTest:testFlzCompressDecompress() (gas: 1902288)
LibZipTest:testFlzCompressDecompress(bytes) (runs: 256, μ: 833972, ~: 673892)
LibZipTest:testFlzCompressDecompress2() (gas: 938925)
LibZipTest:test__codesize() (gas: 21669)
MerkleProofLibTest:testEmptyCalldataHelpers() (gas: 1086)
MerkleProofLibTest:testVerifyMultiProof(bool,bool,bool,bool,bytes32) (runs: 256, μ: 779165, ~: 628996)
MerkleProofLibTest:testVerifyMultiProofForHeightOneTree(bool,bool,bool,bool,bool,bool[]) (runs: 256, μ: 37116, ~: 37668)
MerkleProofLibTest:testVerifyMultiProofForHeightTwoTree(bool,bool,bool,bool,bool,bytes32) (runs: 256, μ: 6519, ~: 6489)
MerkleProofLibTest:testVerifyMultiProofForSingleLeaf(bytes32[],uint256) (runs: 256, μ: 952748, ~: 793147)
MerkleProofLibTest:testVerifyMultiProofIsInvalid() (gas: 627912)
MerkleProofLibTest:testVerifyMultiProofIsValid() (gas: 629253)
MerkleProofLibTest:testVerifyMultiProofMalicious() (gas: 7998)
MerkleProofLibTest:testVerifyProof(bytes32[],uint256) (runs: 256, μ: 905939, ~: 777109)
MerkleProofLibTest:testVerifyProofBasicCase(bool,bool,bool,bytes32) (runs: 256, μ: 4077, ~: 4222)
MerkleProofLibTest:testVerifyProofBasicCaseIsInvalid() (gas: 3581)
MerkleProofLibTest:testVerifyProofBasicCaseIsValid() (gas: 3590)
MerkleProofLibTest:testVerifyProofForHeightOneTree(bool,bool,bool,bool) (runs: 256, μ: 2697, ~: 2971)
MerkleProofLibTest:test__codesize() (gas: 12117)
MetadataReaderLibTest:testBoundsCheckDifferential(uint256) (runs: 256, μ: 25565, ~: 25524)
MetadataReaderLibTest:testReadBytes32String() (gas: 662410)
MetadataReaderLibTest:testReadBytes32StringTruncated() (gas: 796157)
MetadataReaderLibTest:testReadString(uint256) (runs: 256, μ: 868273, ~: 722943)
MetadataReaderLibTest:testReadStringChopped() (gas: 310925)
MetadataReaderLibTest:testReadStringTruncated(uint256) (runs: 256, μ: 799541, ~: 706435)
MetadataReaderLibTest:testReadUint() (gas: 911216)
MetadataReaderLibTest:testReadUint(uint256) (runs: 256, μ: 46033, ~: 47093)
MetadataReaderLibTest:test__codesize() (gas: 9186)
MinHeapLibTest:testHeapEnqueue(uint256) (runs: 256, μ: 184422, ~: 178950)
MinHeapLibTest:testHeapEnqueue2(uint256) (runs: 256, μ: 657473, ~: 547172)
MinHeapLibTest:testHeapEnqueueGas() (gas: 856319)
MinHeapLibTest:testHeapPSiftTrick(uint256,uint256,uint256) (runs: 256, μ: 723, ~: 880)
MinHeapLibTest:testHeapPushAndPop(uint256) (runs: 256, μ: 104949, ~: 99761)
MinHeapLibTest:testHeapPushPop(uint256) (runs: 256, μ: 248895, ~: 257269)
MinHeapLibTest:testHeapReplace(uint256) (runs: 256, μ: 295444, ~: 305416)
MinHeapLibTest:testHeapRoot(uint256) (runs: 256, μ: 5232, ~: 5232)
MinHeapLibTest:testHeapSmallest(uint256) (runs: 256, μ: 1778356, ~: 1288158)
MinHeapLibTest:testHeapSmallestGas() (gas: 49973111)
MinHeapLibTest:test__codesize() (gas: 8140)
MulticallableTest:testMulticallableBenchmark() (gas: 29588)
MulticallableTest:testMulticallableOriginalBenchmark() (gas: 38849)
MulticallableTest:testMulticallablePreservesMsgSender() (gas: 11193)
MulticallableTest:testMulticallableReturnDataIsProperlyEncoded() (gas: 11667)
MulticallableTest:testMulticallableReturnDataIsProperlyEncoded(string,string,uint256) (runs: 256, μ: 9976, ~: 12012)
MulticallableTest:testMulticallableReturnDataIsProperlyEncoded(uint256,uint256,uint256,uint256) (runs: 256, μ: 11813, ~: 11813)
MulticallableTest:testMulticallableRevertWithCustomError() (gas: 11841)
MulticallableTest:testMulticallableRevertWithMessage() (gas: 13513)
MulticallableTest:testMulticallableRevertWithMessage(string) (runs: 256, μ: 14126, ~: 13974)
MulticallableTest:testMulticallableRevertWithNothing() (gas: 11726)
MulticallableTest:testMulticallableWithNoData() (gas: 6322)
MulticallableTest:test__codesize() (gas: 9774)
OwnableRolesTest:testBytecodeSize() (gas: 350635)
OwnableRolesTest:testGrantAndRemoveRolesDirect(address,uint256,uint256) (runs: 256, μ: 38440, ~: 40582)
OwnableRolesTest:testGrantAndRevokeOrRenounceRoles(address,bool,bool,bool,uint256,uint256) (runs: 256, μ: 27600, ~: 28129)
OwnableRolesTest:testGrantRoles() (gas: 36122)
OwnableRolesTest:testHandoverOwnership() (gas: 32342)
OwnableRolesTest:testHandoverOwnership(address) (runs: 256, μ: 32402, ~: 32386)
OwnableRolesTest:testHandoverOwnershipAfterExpiration() (gas: 36999)
OwnableRolesTest:testHandoverOwnershipBeforeExpiration() (gas: 28722)
OwnableRolesTest:testHandoverOwnershipRevertsIfCompleteIsNotOwner() (gas: 35585)
OwnableRolesTest:testHandoverOwnershipWithCancellation() (gas: 30715)
OwnableRolesTest:testHasAllRoles(address,uint256,uint256,uint256,bool) (runs: 256, μ: 33540, ~: 33537)
OwnableRolesTest:testHasAnyRole(address,uint256,uint256) (runs: 256, μ: 32710, ~: 33332)
OwnableRolesTest:testInitializeOwnerDirect() (gas: 16724)
OwnableRolesTest:testOnlyOwnerModifier(address,bool) (runs: 256, μ: 23708, ~: 32884)
OwnableRolesTest:testOnlyOwnerOrRolesModifier() (gas: 36582)
OwnableRolesTest:testOnlyOwnerOrRolesModifier(address,bool,uint256,uint256) (runs: 256, μ: 55705, ~: 57924)
OwnableRolesTest:testOnlyRolesModifier(address,uint256,uint256) (runs: 256, μ: 53891, ~: 57912)
OwnableRolesTest:testOnlyRolesOrOwnerModifier(address,bool,uint256,uint256) (runs: 256, μ: 56866, ~: 58382)
OwnableRolesTest:testOrdinalsFromRoles() (gas: 3610415)
OwnableRolesTest:testOrdinalsFromRoles(uint256) (runs: 256, μ: 70634, ~: 37339)
OwnableRolesTest:testOwnershipHandoverValidForDefaultValue() (gas: 5825)
OwnableRolesTest:testRenounceOwnership() (gas: 12899)
OwnableRolesTest:testRolesFromOrdinals() (gas: 651259)
OwnableRolesTest:testRolesFromOrdinals(uint8[]) (runs: 256, μ: 78088, ~: 74221)
OwnableRolesTest:testSetOwnerDirect() (gas: 17776)
OwnableRolesTest:testSetOwnerDirect(address) (runs: 256, μ: 17901, ~: 17901)
OwnableRolesTest:testSetRolesDirect(uint256) (runs: 256, μ: 99843, ~: 99819)
OwnableRolesTest:testTransferOwnership() (gas: 19657)
OwnableRolesTest:testTransferOwnership(address,bool,bool) (runs: 256, μ: 14498, ~: 12721)
OwnableRolesTest:test__codesize() (gas: 23724)
OwnableTest:testBytecodeSize() (gas: 235158)
OwnableTest:testHandoverOwnership() (gas: 32072)
OwnableTest:testHandoverOwnership(address) (runs: 256, μ: 32094, ~: 32081)
OwnableTest:testHandoverOwnershipAfterExpiration() (gas: 36711)
OwnableTest:testHandoverOwnershipBeforeExpiration() (gas: 28571)
OwnableTest:testHandoverOwnershipRevertsIfCompleteIsNotOwner() (gas: 35341)
OwnableTest:testHandoverOwnershipWithCancellation() (gas: 30318)
OwnableTest:testInitializeOwnerDirect() (gas: 16599)
OwnableTest:testOnlyOwnerModifier(address,bool) (runs: 256, μ: 23624, ~: 32828)
OwnableTest:testOwnershipHandoverValidForDefaultValue() (gas: 5737)
OwnableTest:testRenounceOwnership() (gas: 12755)
OwnableTest:testSetOwnerDirect() (gas: 17686)
OwnableTest:testSetOwnerDirect(address) (runs: 256, μ: 17801, ~: 17812)
OwnableTest:testTransferOwnership() (gas: 19467)
OwnableTest:testTransferOwnership(address,bool,bool) (runs: 256, μ: 14246, ~: 12487)
OwnableTest:test__codesize() (gas: 12253)
ReceiverTest:testETHReceived() (gas: 9621)
ReceiverTest:testOnERC1155BatchReceived() (gas: 48975)
ReceiverTest:testOnERC1155Received() (gas: 46717)
ReceiverTest:testOnERC721Received() (gas: 64127)
ReceiverTest:test__codesize() (gas: 3310)
RedBlackTreeLibTest:testRedBlackTreeBenchUint160() (gas: 3429423)
RedBlackTreeLibTest:testRedBlackTreeBenchUint256() (gas: 5843130)
RedBlackTreeLibTest:testRedBlackTreeInsertAndRemove(uint256) (runs: 256, μ: 627514, ~: 487609)
RedBlackTreeLibTest:testRedBlackTreeInsertAndRemove2(uint256) (runs: 256, μ: 420920, ~: 390160)
RedBlackTreeLibTest:testRedBlackTreeInsertAndRemove3() (gas: 21546528)
RedBlackTreeLibTest:testRedBlackTreeInsertBenchStep() (gas: 3703676)
RedBlackTreeLibTest:testRedBlackTreeInsertBenchUint160() (gas: 3469294)
RedBlackTreeLibTest:testRedBlackTreeInsertBenchUint256() (gas: 6368914)
RedBlackTreeLibTest:testRedBlackTreeInsertOneGas() (gas: 45564)
RedBlackTreeLibTest:testRedBlackTreeInsertTenGas() (gas: 282751)
RedBlackTreeLibTest:testRedBlackTreeInsertThreeGas() (gas: 96193)
RedBlackTreeLibTest:testRedBlackTreeInsertTwoGas() (gas: 69797)
RedBlackTreeLibTest:testRedBlackTreeNearest(uint256) (runs: 256, μ: 228165, ~: 224911)
RedBlackTreeLibTest:testRedBlackTreeNearestAfter(uint256) (runs: 256, μ: 251011, ~: 248814)
RedBlackTreeLibTest:testRedBlackTreeNearestBefore(uint256) (runs: 256, μ: 232084, ~: 195909)
RedBlackTreeLibTest:testRedBlackTreePointers() (gas: 91897)
RedBlackTreeLibTest:testRedBlackTreeRejectsEmptyValue() (gas: 3238)
RedBlackTreeLibTest:testRedBlackTreeRemoveViaPointer() (gas: 58172)
RedBlackTreeLibTest:testRedBlackTreeTreeFullReverts() (gas: 50335)
RedBlackTreeLibTest:testRedBlackTreeTryInsertAndRemove() (gas: 56139)
RedBlackTreeLibTest:testRedBlackTreeValues() (gas: 191599)
RedBlackTreeLibTest:testRedBlackTreeValues(uint256) (runs: 256, μ: 383999, ~: 267070)
RedBlackTreeLibTest:test__codesize() (gas: 14087)
ReentrancyGuardTest:testRecursiveDirectUnguardedCall() (gas: 34254)
ReentrancyGuardTest:testRecursiveIndirectUnguardedCall() (gas: 47771)
ReentrancyGuardTest:testRevertGuardLocked() (gas: 53917)
ReentrancyGuardTest:testRevertReadGuardLocked() (gas: 53620)
ReentrancyGuardTest:testRevertRecursiveDirectGuardedCall() (gas: 55168)
ReentrancyGuardTest:testRevertRecursiveIndirectGuardedCall() (gas: 56574)
ReentrancyGuardTest:testRevertRemoteCallback() (gas: 56208)
ReentrancyGuardTest:test__codesize() (gas: 5563)
SSTORE2Test:testReadInvalidPointerCustomBoundsReverts() (gas: 3242)
SSTORE2Test:testReadInvalidPointerCustomBoundsReverts(address,uint256,uint256) (runs: 256, μ: 769924, ~: 629512)
SSTORE2Test:testReadInvalidPointerCustomStartBoundReverts() (gas: 3241)
SSTORE2Test:testReadInvalidPointerCustomStartBoundReverts(address,uint256) (runs: 256, μ: 692029, ~: 630014)
SSTORE2Test:testReadInvalidPointerRevert(address) (runs: 256, μ: 754053, ~: 629491)
SSTORE2Test:testReadInvalidPointerReverts() (gas: 3215)
SSTORE2Test:testWriteRead() (gas: 69590)
SSTORE2Test:testWriteRead(bytes) (runs: 256, μ: 876438, ~: 671695)
SSTORE2Test:testWriteReadCustomBounds() (gas: 34463)
SSTORE2Test:testWriteReadCustomBounds(bytes,uint256,uint256) (runs: 256, μ: 725451, ~: 662901)
SSTORE2Test:testWriteReadCustomBoundsOutOfRangeReverts(bytes,uint256,uint256) (runs: 256, μ: 861477, ~: 667621)
SSTORE2Test:testWriteReadCustomStartBound() (gas: 34651)
SSTORE2Test:testWriteReadCustomStartBound(bytes,uint256) (runs: 256, μ: 732600, ~: 669107)
SSTORE2Test:testWriteReadCustomStartBoundOutOfRangeReverts(bytes,uint256) (runs: 256, μ: 812894, ~: 667626)
SSTORE2Test:testWriteReadDeterministic(bytes) (runs: 256, μ: 834639, ~: 743165)
SSTORE2Test:testWriteReadEmptyBound() (gas: 33825)
SSTORE2Test:testWriteReadEmptyOutOfBoundsReverts() (gas: 36485)
SSTORE2Test:testWriteReadFullBoundedRead() (gas: 69630)
SSTORE2Test:testWriteReadFullStartBound() (gas: 34815)
SSTORE2Test:testWriteReadOutOfBoundsReverts() (gas: 36463)
SSTORE2Test:testWriteReadOutOfStartBoundReverts() (gas: 36467)
SSTORE2Test:testWriteWithTooBigDataReverts() (gas: 97662218)
SSTORE2Test:test__codesize() (gas: 9603)
SafeCastLibTest:testSafeCastToInt(int256) (runs: 256, μ: 4384, ~: 3415)
SafeCastLibTest:testSafeCastToInt256(uint256) (runs: 256, μ: 931, ~: 390)
SafeCastLibTest:testSafeCastToIntBench() (gas: 383456)
SafeCastLibTest:testSafeCastToUint() (gas: 10560)
SafeCastLibTest:testSafeCastToUint(uint256) (runs: 256, μ: 4150, ~: 3351)
SafeCastLibTest:testSafeCastToUint256(int256) (runs: 256, μ: 1285, ~: 432)
SafeCastLibTest:testSafeCastToUintBench() (gas: 326306)
SafeCastLibTest:test__codesize() (gas: 16001)
SafeTransferLibTest:testApproveWithGarbageReverts(address,uint256) (runs: 256, μ: 111346, ~: 123373)
SafeTransferLibTest:testApproveWithMissingReturn() (gas: 31945)
SafeTransferLibTest:testApproveWithMissingReturn(address,uint256) (runs: 256, μ: 30860, ~: 32104)
SafeTransferLibTest:testApproveWithNonContract() (gas: 2989)
SafeTransferLibTest:testApproveWithNonContract(address,address,uint256) (runs: 256, μ: 3524, ~: 3536)
SafeTransferLibTest:testApproveWithNonGarbage(address,uint256) (runs: 256, μ: 78267, ~: 59187)
SafeTransferLibTest:testApproveWithRetry() (gas: 768579)
SafeTransferLibTest:testApproveWithRetry(address,uint256,uint256) (runs: 256, μ: 767124, ~: 768723)
SafeTransferLibTest:testApproveWithRetryWithNonContract() (gas: 2990)
SafeTransferLibTest:testApproveWithRetryWithNonContract(address,address,uint256) (runs: 256, μ: 3521, ~: 3533)
SafeTransferLibTest:testApproveWithReturnsFalseReverts() (gas: 9515)
SafeTransferLibTest:testApproveWithReturnsFalseReverts(address,uint256) (runs: 256, μ: 9652, ~: 9652)
SafeTransferLibTest:testApproveWithReturnsTooLittleReverts() (gas: 9419)
SafeTransferLibTest:testApproveWithReturnsTooLittleReverts(address,uint256) (runs: 256, μ: 9603, ~: 9603)
SafeTransferLibTest:testApproveWithReturnsTooMuch() (gas: 32324)
SafeTransferLibTest:testApproveWithReturnsTooMuch(address,uint256) (runs: 256, μ: 31266, ~: 32510)
SafeTransferLibTest:testApproveWithReturnsTwoReverts(address,uint256) (runs: 256, μ: 9675, ~: 9675)
SafeTransferLibTest:testApproveWithRevertingReverts() (gas: 9376)
SafeTransferLibTest:testApproveWithRevertingReverts(address,uint256) (runs: 256, μ: 9570, ~: 9570)
SafeTransferLibTest:testApproveWithStandardERC20() (gas: 31786)
SafeTransferLibTest:testApproveWithStandardERC20(address,uint256) (runs: 256, μ: 30725, ~: 31969)
SafeTransferLibTest:testBalanceOfStandardERC20() (gas: 7830)
SafeTransferLibTest:testBalanceOfStandardERC20(address,uint256) (runs: 256, μ: 39497, ~: 40437)
SafeTransferLibTest:testForceTransferETHToGriever() (gas: 1511304)
SafeTransferLibTest:testForceTransferETHToGriever(uint256,uint256) (runs: 256, μ: 529645, ~: 570828)
SafeTransferLibTest:testTransferAllETH() (gas: 34598)
SafeTransferLibTest:testTransferAllETH(address) (runs: 256, μ: 35018, ~: 35018)
SafeTransferLibTest:testTransferAllETHToContractWithoutFallbackReverts() (gas: 10788)
SafeTransferLibTest:testTransferAllETHToContractWithoutFallbackReverts(uint256) (runs: 256, μ: 10898, ~: 10898)
SafeTransferLibTest:testTransferAllFromWithStandardERC20() (gas: 33092)
SafeTransferLibTest:testTransferAllFromWithStandardERC20(address,address,uint256) (runs: 256, μ: 56523, ~: 56524)
SafeTransferLibTest:testTransferAllWithStandardERC20() (gas: 30735)
SafeTransferLibTest:testTransferAllWithStandardERC20(address,uint256) (runs: 256, μ: 42114, ~: 42114)
SafeTransferLibTest:testTransferETH() (gas: 34600)
SafeTransferLibTest:testTransferETH(address,uint256) (runs: 256, μ: 32688, ~: 35250)
SafeTransferLibTest:testTransferETHToContractWithoutFallbackReverts() (gas: 10846)
SafeTransferLibTest:testTransferETHToContractWithoutFallbackReverts(uint256) (runs: 256, μ: 10546, ~: 10928)
SafeTransferLibTest:testTransferFromWithGarbageReverts(address,address,uint256) (runs: 256, μ: 969187, ~: 782733)
SafeTransferLibTest:testTransferFromWithMissingReturn() (gas: 670582)
SafeTransferLibTest:testTransferFromWithMissingReturn(address,address,uint256) (runs: 256, μ: 769470, ~: 670123)
SafeTransferLibTest:testTransferFromWithNonContract() (gas: 3022)
SafeTransferLibTest:testTransferFromWithNonContract(address,address,address,uint256) (runs: 256, μ: 3635, ~: 3694)
SafeTransferLibTest:testTransferFromWithNonGarbage(address,address,uint256) (runs: 256, μ: 873710, ~: 720176)
SafeTransferLibTest:testTransferFromWithReturnsFalseReverts() (gas: 635370)
SafeTransferLibTest:testTransferFromWithReturnsFalseReverts(address,address,uint256) (runs: 256, μ: 733696, ~: 635832)
SafeTransferLibTest:testTransferFromWithReturnsTooLittleReverts() (gas: 635579)
SafeTransferLibTest:testTransferFromWithReturnsTooLittleReverts(address,address,uint256) (runs: 256, μ: 700020, ~: 635690)
SafeTransferLibTest:testTransferFromWithReturnsTooMuch() (gas: 670350)
SafeTransferLibTest:testTransferFromWithReturnsTooMuch(address,address,uint256) (runs: 256, μ: 744340, ~: 670792)
SafeTransferLibTest:testTransferFromWithReturnsTwoReverts(address,address,uint256) (runs: 256, μ: 746697, ~: 635837)
SafeTransferLibTest:testTransferFromWithRevertingReverts() (gas: 629190)
SafeTransferLibTest:testTransferFromWithRevertingReverts(address,address,uint256) (runs: 256, μ: 746897, ~: 629297)
SafeTransferLibTest:testTransferFromWithStandardERC20() (gas: 668120)
SafeTransferLibTest:testTransferFromWithStandardERC20(address,address,uint256) (runs: 256, μ: 745896, ~: 667661)
SafeTransferLibTest:testTransferWithGarbageReverts(address,uint256) (runs: 256, μ: 870332, ~: 748055)
SafeTransferLibTest:testTransferWithMissingReturn() (gas: 655954)
SafeTransferLibTest:testTransferWithMissingReturn(address,uint256) (runs: 256, μ: 801380, ~: 656545)
SafeTransferLibTest:testTransferWithNonContract() (gas: 2988)
SafeTransferLibTest:testTransferWithNonContract(address,address,uint256) (runs: 256, μ: 3545, ~: 3557)
SafeTransferLibTest:testTransferWithNonGarbage(address,uint256) (runs: 256, μ: 839866, ~: 725592)
SafeTransferLibTest:testTransferWithReturnsFalseReverts() (gas: 632501)
SafeTransferLibTest:testTransferWithReturnsFalseReverts(address,uint256) (runs: 256, μ: 748610, ~: 632998)
SafeTransferLibTest:testTransferWithReturnsTooLittleReverts() (gas: 633070)
SafeTransferLibTest:testTransferWithReturnsTooLittleReverts(address,uint256) (runs: 256, μ: 777282, ~: 632831)
SafeTransferLibTest:testTransferWithReturnsTooMuch() (gas: 656421)
SafeTransferLibTest:testTransferWithReturnsTooMuch(address,uint256) (runs: 256, μ: 781040, ~: 657024)
SafeTransferLibTest:testTransferWithReturnsTwoReverts(address,uint256) (runs: 256, μ: 726588, ~: 632907)
SafeTransferLibTest:testTransferWithRevertingReverts() (gas: 632327)
SafeTransferLibTest:testTransferWithRevertingReverts(address,uint256) (runs: 256, μ: 816451, ~: 632931)
SafeTransferLibTest:testTransferWithStandardERC20() (gas: 657003)
SafeTransferLibTest:testTransferWithStandardERC20(address,uint256) (runs: 256, μ: 819318, ~: 656785)
SafeTransferLibTest:testTryTransferAllETH() (gas: 148698)
SafeTransferLibTest:testTryTransferETH() (gas: 148745)
SafeTransferLibTest:testTryTransferETHWithNoGrief() (gas: 537083)
SafeTransferLibTest:testTryTransferETHWithNoStorageWrites() (gas: 192518)
SafeTransferLibTest:test__codesize() (gas: 37534)
SignatureCheckerLibTest:testEmptyCalldataHelpers() (gas: 3972)
SignatureCheckerLibTest:testSignatureChecker(bytes32) (runs: 256, μ: 57174, ~: 46824)
SignatureCheckerLibTest:testSignatureCheckerOnEOAWithInvalidSignature() (gas: 21228)
SignatureCheckerLibTest:testSignatureCheckerOnEOAWithInvalidSigner() (gas: 30728)
SignatureCheckerLibTest:testSignatureCheckerOnEOAWithMatchingSignerAndSignature() (gas: 17670)
SignatureCheckerLibTest:testSignatureCheckerOnEOAWithWrongSignedMessageHash() (gas: 21251)
SignatureCheckerLibTest:testSignatureCheckerOnMaliciousWallet() (gas: 31820)
SignatureCheckerLibTest:testSignatureCheckerOnWalletWithInvalidSignature() (gas: 77090)
SignatureCheckerLibTest:testSignatureCheckerOnWalletWithInvalidSigner() (gas: 25608)
SignatureCheckerLibTest:testSignatureCheckerOnWalletWithMatchingSignerAndSignature() (gas: 64034)
SignatureCheckerLibTest:testSignatureCheckerOnWalletWithWrongSignedMessageHash() (gas: 64091)
SignatureCheckerLibTest:testSignatureCheckerOnWalletWithZeroAddressSigner() (gas: 12264)
SignatureCheckerLibTest:testToEthSignedMessageHashDifferential(bytes) (runs: 256, μ: 1304, ~: 1305)
SignatureCheckerLibTest:testToEthSignedMessageHashDifferential(bytes32) (runs: 256, μ: 526, ~: 526)
SignatureCheckerLibTest:test__codesize() (gas: 11030)
SoladyTest:test__codesize() (gas: 1102)
TestPlus:test__codesize() (gas: 406)
UUPSUpgradeableTest:testNotDelegatedGuard() (gas: 15875)
UUPSUpgradeableTest:testOnlyProxyGuard() (gas: 8896)
UUPSUpgradeableTest:testUpgradeTo() (gas: 287374)
UUPSUpgradeableTest:testUpgradeToAndCall() (gas: 309608)
UUPSUpgradeableTest:testUpgradeToAndCallRevertWithCustomError() (gas: 286618)
UUPSUpgradeableTest:testUpgradeToAndCallRevertWithUnauthorized() (gas: 15964)
UUPSUpgradeableTest:testUpgradeToAndCallRevertWithUpgradeFailed() (gas: 18264)
UUPSUpgradeableTest:testUpgradeToRevertWithUnauthorized() (gas: 16143)
UUPSUpgradeableTest:testUpgradeToRevertWithUpgradeFailed() (gas: 18464)
UUPSUpgradeableTest:test__codesize() (gas: 7195)
WETHInvariants:invariantTotalSupplyEqualsBalance() (runs: 256, calls: 3840, reverts: 1772)
WETHInvariants:test__codesize() (gas: 6008)
WETHTest:testDeposit() (gas: 68090)
WETHTest:testDeposit(uint256) (runs: 256, μ: 65900, ~: 68384)
WETHTest:testFallbackDeposit() (gas: 67796)
WETHTest:testFallbackDeposit(uint256) (runs: 256, μ: 65561, ~: 68045)
WETHTest:testMetadata() (gas: 10098)
WETHTest:testPartialWithdraw() (gas: 79566)
WETHTest:testWithdraw() (gas: 59332)
WETHTest:testWithdraw(uint256,uint256) (runs: 256, μ: 77010, ~: 80326)
WETHTest:testWithdrawToContractWithoutReceiveReverts() (gas: 93923)
WETHTest:test__codesize() (gas: 11080)