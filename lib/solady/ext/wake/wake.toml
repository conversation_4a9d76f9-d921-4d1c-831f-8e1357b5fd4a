[compiler.solc]
exclude_paths = ["node_modules", "venv", ".venv", "lib", "script", "test"]
include_paths = ["node_modules"]
remappings = [
    "forge-std/=test/utils/forge-std/",
]

[compiler.solc.optimizer]
enabled = true
runs = 1000

[detectors]
exclude = ["unused-contract", "unused-import"]
ignore_paths = ["venv", ".venv", "test"]
exclude_paths = ["node_modules", "lib", "script"]

[testing]
cmd = "anvil"

[testing.anvil]
cmd_args = "--prune-history 100 --transaction-block-keeper 10 --steps-tracing --silent"

[testing.ganache]
cmd_args = "-k istanbul -q"

[testing.hardhat]
cmd_args = ""