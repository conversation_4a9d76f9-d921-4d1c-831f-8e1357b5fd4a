// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";
import { Ownable2Step } from "@openzeppelin/contracts/access/Ownable2Step.sol";

import { Round } from "src/types/Round.sol";
import { Presale, Membership } from "src/types/Configuration.sol";
import { Fees } from "src/types/Fees.sol";
import { Errors } from "src/libraries/Errors.sol";
import { IVestFeeCollectorProvider } from "src/IVestFeeCollectorProvider.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { IVestPresaleDeployer } from "src/VestPresaleDeployer.sol";
import { IVestPresaleScheduler } from "src/IVestPresaleScheduler.sol";
import { IVestMembershipDeployer } from "src/VestMembershipDeployer.sol";

/**
 * @title VestController
 * @notice An implementation of smart contract to handle the creation of presales.
 */
contract VestController is Ownable2Step, IVestFeeCollectorProvider {
    /// @notice Fees configuration.
    Fees internal fees;

    /// @notice Reference to external deployer smart contract.
    IVestPresaleDeployer internal immutable presaleDeployer;

    /// @notice Reference to external deployer smart contract.
    IVestMembershipDeployer internal immutable membershipDeployer;

    /// @notice Event emitted when the fees have been updated.
    event FeesUpdated(Fees);

    /// @notice Given fees cannot be used.
    error FeesDontMatch();

    /// @notice Contract state initialization.
    /// @param presaleDeployer_ Address of the external smart deployer.
    /// @param membershipDeployer_ Address of the external smart deployer.
    /// @param fees_ Configuration of the fees.
    constructor(IVestPresaleDeployer presaleDeployer_, IVestMembershipDeployer membershipDeployer_, Fees memory fees_)
        Ownable(_msgSender())
    {
        _validateFees(fees_);

        fees = fees_;
        presaleDeployer = presaleDeployer_;
        membershipDeployer = membershipDeployer_;

        emit FeesUpdated(fees_);
    }

    /// @notice Creates new presale and membership.
    /// @param presaleConfig Configuration of the presale.
    /// @param rounds Rounds of the presale.
    /// @param membershipConfig Configuration of the membership.
    function createPresale(
        Presale.Configuration memory presaleConfig,
        Round[] memory rounds,
        Membership.Configuration memory membershipConfig
    ) external returns (address, address) {
        Fees memory fees_ = Fees({
            tokenANumerator: presaleConfig.fees.tokenANumerator,
            tokenADenominator: presaleConfig.fees.tokenADenominator,
            tokenBNumerator: presaleConfig.fees.tokenBNumerator,
            tokenBDenominator: presaleConfig.fees.tokenBDenominator,
            nftNumerator: membershipConfig.fees.numerator,
            nftDenominator: membershipConfig.fees.denominator
        });

        if (_msgSender() == owner()) {
            _validateFees(fees_);
        } else {
            _ensureEqualFees(fees, fees_);
        }

        address presale = presaleDeployer.predict();
        address membership = membershipDeployer.predict();

        membershipDeployer.deploy(presale, IVestPresaleScheduler(presale), this, membershipConfig);

        presaleDeployer.deploy(IVestMembership(membership), this, presaleConfig, rounds);

        return (presale, membership);
    }

    /// @notice Updates fees configuration.
    /// @param fees_ Configuration of the fees.
    function setFees(Fees memory fees_) external onlyOwner {
        _validateFees(fees_);

        fees = fees_;

        emit FeesUpdated(fees_);
    }

    /// @inheritdoc IVestFeeCollectorProvider
    function getFeeCollector() external view returns (address) {
        return owner();
    }

    /// @notice Returns the fees configuration.
    function getFees() external view returns (Fees memory fees_) {
        fees_ = fees;
    }

    /// @notice Checks if given fees configurations are equal.
    /// @param fees1 Configuration to check.
    /// @param fees2 Configuration to check.
    function _ensureEqualFees(Fees memory fees1, Fees memory fees2) internal pure {
        if (
            fees1.tokenANumerator != fees2.tokenANumerator || fees1.tokenADenominator != fees2.tokenADenominator
                || fees1.tokenBNumerator != fees2.tokenBNumerator || fees1.tokenBDenominator != fees2.tokenBDenominator
                || fees1.nftNumerator != fees2.nftNumerator || fees1.nftDenominator != fees2.nftDenominator
        ) {
            revert FeesDontMatch();
        }
    }

    /// @notice Validates whether a given fees configuration meets the requirements.
    /// @param fees_ Configuration to validate.
    function _validateFees(Fees memory fees_) internal pure {
        if (
            fees_.tokenADenominator == 0 || fees_.tokenBDenominator == 0 || fees_.nftDenominator == 0
                || fees_.tokenANumerator >= fees_.tokenADenominator || fees_.tokenBNumerator >= fees_.tokenBDenominator
                || fees_.nftNumerator >= fees_.nftDenominator
        ) {
            revert Errors.UnacceptableValue();
        }
    }
}
