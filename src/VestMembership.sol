// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";
import { ERC721 } from "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import { IERC721 } from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import { IERC721Metadata } from "@openzeppelin/contracts/interfaces/IERC721Metadata.sol";
import { IERC2981 } from "@openzeppelin/contracts/interfaces/IERC2981.sol";

import { Errors } from "src/libraries/Errors.sol";
import { Boolean } from "src/libraries/Boolean.sol";
import { Membership } from "src/types/Configuration.sol";
import { DynamicIds } from "src/libraries/DynamicIds.sol";
import { ERC721DynamicIds } from "src/utils/ERC721DynamicIds.sol";

import { IVestMembership } from "src/IVestMembership.sol";
import { IVestMembershipDescriptor } from "src/VestMembershipDescriptor.sol";
import { IVestPresaleScheduler } from "src/IVestPresaleScheduler.sol";
import { IVestFeeCollectorProvider } from "./IVestFeeCollectorProvider.sol";

/**
 * @title VestMembership
 * @notice An implementation of smart contract representing membership in a presale.
 */
contract VestMembership is IERC2981, ERC721DynamicIds, IVestMembership, Ownable {
    /// @notice Collection of the metadata.
    Metadata internal _metadata;

    /// @notice Fees configuration.
    Membership.Fees internal _fees;

    /// @notice Reference to external scheduler contract.
    IVestPresaleScheduler internal immutable _scheduler;

    /// @notice Reference to external descriptor contract.
    IVestMembershipDescriptor internal immutable _descriptor;

    /// @notice Reference to external fee collector provider.
    IVestFeeCollectorProvider internal immutable _feeCollectorProvider;

    /// @notice Information about the usage by membership.
    mapping(uint256 mintId => Usage) internal _usages;

    /// @notice Information about the membership round.
    mapping(uint256 mintId => uint256) internal _rounds;

    /// @notice Collection of the attributes of each membership.
    mapping(uint256 mintId => Attributes) internal _attributes;

    /// @notice Transfer not allowed for the given membership.
    /// @param publicId Id of the membership.
    error TransferNotAllowed(uint256 publicId);

    /// @notice Contract state initialization.
    /// @param scheduler Address of the external scheduler.
    /// @param feeCollectorProvider Address of the external fee collector provider.
    /// @param configuration Configuration of the membership.
    constructor(
        address owner_,
        IVestPresaleScheduler scheduler,
        IVestFeeCollectorProvider feeCollectorProvider,
        Membership.Configuration memory configuration
    )
        Ownable(owner_)
        ERC721(
            configuration.descriptor.name(configuration.metadata),
            configuration.descriptor.symbol(configuration.metadata)
        )
    {
        if (address(scheduler) == address(0)) revert Errors.UnacceptableReference();
        if (address(feeCollectorProvider) == address(0)) revert Errors.UnacceptableReference();

        _fees = configuration.fees;
        _scheduler = scheduler;
        _metadata = configuration.metadata;
        _descriptor = configuration.descriptor;
        _feeCollectorProvider = feeCollectorProvider;
    }

    /**
     * Increases the usage.current
     * @notice This function does no validation except for the valid id.
     * It’s up to the consumer to ensure any invariants.
     * @param publicId publicId of an NFT
     * @param amount usage.current increases by amount
     */
    function consume(uint256 publicId, uint256 amount) public onlyOwner returns (uint256) {
        uint256 mintId = _requireValidPublicId(publicId);
        _usages[mintId].current += amount;

        return _updatePublicId(publicId, mintId);
    }

    /**
     * Increases the usage.max
     * @notice This function does no validation except for the valid id.
     * It’s up to the consumer to ensure any invariants.
     * @param publicId publicId of an NFT
     * @param amount usage.max increases by amount
     */
    function extend(uint256 publicId, uint256 amount) public onlyOwner returns (uint256) {
        uint256 mintId = _requireValidPublicId(publicId);
        _usages[mintId].max += amount;
        return _updatePublicId(publicId, mintId);
    }

    /**
     * Decreases the usage.max
     * @notice This function does no validation except for the valid id.
     * It’s up to the consumer to ensure any invariants.
     * @param publicId publicId of an NFT
     * @param amount usage.max subtrahend
     */
    function reduce(uint256 publicId, uint256 amount) public onlyOwner returns (uint256) {
        uint256 mintId = _requireValidPublicId(publicId);
        _usages[mintId].max -= amount;
        return _updatePublicId(publicId, mintId);
    }

    /// @inheritdoc IVestMembership
    function getRoundId(uint256 publicId) external view returns (uint256) {
        uint256 mintId = _requireValidPublicId(publicId);
        return _rounds[mintId];
    }

    /// @inheritdoc IVestMembership
    function unlocked(uint256 publicId) external view returns (uint256) {
        uint256 mintId = _requireValidPublicId(publicId);
        uint256 start = getStartTimestamp();
        uint256 allocation = _usages[mintId].max;
        IVestMembership.Attributes memory attributes = _attributes[mintId];

        return unlocked(start, allocation, attributes);
    }

    /// @inheritdoc IVestMembership
    function getStartTimestamp() public view returns (uint256) {
        return _scheduler.getTgeTimestamp();
    }

    /// @inheritdoc IVestMembership
    function unlocked(uint256 start, uint256 allocation, IVestMembership.Attributes memory attributes)
        public
        view
        returns (uint256)
    {
        uint256 timestamp = block.timestamp;

        if (timestamp < start) return 0;

        uint256 duration = attributes.vestingPeriodCount * attributes.vestingPeriodDuration + attributes.cliffDuration;

        if (timestamp >= start + duration) return allocation;

        uint256 tge = (allocation * attributes.tgeNumerator) / attributes.tgeDenominator;

        if (timestamp < start + attributes.cliffDuration) return tge;

        uint256 cliffUnlock = (allocation * attributes.cliffNumerator) / attributes.cliffDenominator;

        uint256 elapsedTime = timestamp - (start + attributes.cliffDuration);
        uint256 elapsedPeriods = elapsedTime / attributes.vestingPeriodDuration;

        if (attributes.cliffDuration != 0 && attributes.cliffNumerator == 0) elapsedPeriods = elapsedPeriods + 1;

        return tge + cliffUnlock + ((allocation - tge - cliffUnlock) * elapsedPeriods) / attributes.vestingPeriodCount;
    }

    /// @inheritdoc IVestMembership
    function mint(address owner_, uint256 roundId, uint256 currentUsage, uint256 maxUsage, Attributes memory attributes)
        public
        virtual
        onlyOwner
        returns (uint256 publicId)
    {
        uint256 mintId = DynamicIds.createMintId(abi.encodePacked(owner_, roundId, maxUsage, block.timestamp));

        Usage memory usage = Usage({ current: currentUsage, max: maxUsage });
        _rounds[mintId] = roundId;
        _usages[mintId] = usage;
        _attributes[mintId] = attributes;

        bytes memory data = abi.encode(usage, roundId, attributes);
        publicId = _mintDynamicIdNFT(owner_, mintId, data);
    }

    /// @inheritdoc IERC721
    /// @dev Overriding to implement custom `transferFrom` blocking rules.
    function transferFrom(address from, address to, uint256 publicId) public override(ERC721DynamicIds, IERC721) {
        if (_msgSender() != ownerOf(publicId)) {
            IVestMembership.Attributes memory attributes = getAttributes(publicId);

            // Transferring is blocked when the `tradeable` attribute is equal to false.
            if (attributes.tradeable == Boolean.FALSE) revert TransferNotAllowed(publicId);

            // Transferring is blocked before listing timestamp.
            if (_scheduler.getListingTimestamp() == 0 || block.timestamp < _scheduler.getListingTimestamp()) {
                revert TransferNotAllowed(publicId);
            }
        }

        super.transferFrom(from, to, publicId);
    }

    /// @inheritdoc IVestMembership
    function getUsage(uint256 publicId) public view returns (Usage memory) {
        uint256 mintId = _requireValidPublicId(publicId);
        return _usages[mintId];
    }

    /// @inheritdoc IVestMembership
    function getAttributes(uint256 publicId) public view returns (Attributes memory) {
        uint256 mintId = _requireValidPublicId(publicId);
        return _attributes[mintId];
    }

    /// @inheritdoc IERC721Metadata
    function tokenURI(uint256 publicId) public view override returns (string memory) {
        uint256 mintId = _requireValidPublicId(publicId);

        _requireOwned(mintId);

        return _descriptor.tokenURI(getStartTimestamp(), _usages[mintId], _metadata, _attributes[mintId]);
    }

    /// @inheritdoc IERC2981
    function royaltyInfo(uint256, uint256 salePrice) public view virtual returns (address, uint256) {
        uint256 royaltyAmount = (salePrice * _fees.numerator) / _fees.denominator;

        return (_feeCollectorProvider.getFeeCollector(), royaltyAmount);
    }

    /// @inheritdoc ERC721DynamicIds
    function _getPayload(uint256 mintId) internal view override returns (bytes memory payload) {
        return abi.encode(_usages[mintId]);
    }
}
