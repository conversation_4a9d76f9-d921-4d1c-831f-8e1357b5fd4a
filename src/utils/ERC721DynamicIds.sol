// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { ERC721 } from "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import { IERC721 } from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import { ERC721Enumerable } from "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";

import { DynamicIds } from "src/libraries/DynamicIds.sol";

/**
 * @dev ERC721DynamicIds is a clever trick to make trading NFTs with dynamic traits safe.
 *
 * Stable value represented by an NFT is a prerequisite for safe trading today.
 *
 * If the value of an NFT can be changed by the seller, they can rug the buyer by using up the NFT before they accept they buy offer.
 *
 * Dynamic ids solve that problem by burning the old NFT and minting a new one every time the value of an NFT changes.
 * That invalidates all offers made for an NFT before the value changed.
 *
 * A naive implementation would perform literal burn and mint every time but that’s extremely gas-inefficient.
 *
 * A clever implementation changes NFT id every time its value changes, emits Transfer events as if the NFT was
 * burned and minted, while in reality no storage writes are performed.
 *
 * For this to work, we use 32-byte numbers as ids where:
 * - the first 16 bytes are calculated during the mint and never change again.
 * - the last 16 bytes are calculated every time the value changes.
 *
 * This results in a new id being issued every time the value changes while keeping some unique part of it (the first 16 bytes)
 * constant so that we can keep track of owners, allowances, and other properties.
 *
 * An id emitted in Transfer events and visible to the outside world is called a publicId.
 * An id used internally to keep track of things is called a mintId.
 *
 * The last 16 bytes of mintId MUST equal zero.
 *
 * A watchful reader will notice that by splitting ids in two 16-bytes long parts, we increase the risk of id collision.
 * This risk is described by The Birthday Problem.
 * For 16-bytes long ids, the risk of collision raises above 1% after generating 52 * 10^18 perfectly random ids.
 * That makes a collision unlikely for most protocols using this technique.
 *
 * Note that _safeMint function prevents minting NFTs with an id that already exists.
 *
 * To prevent DOS due to id collision, it’s advised protocols mix in block.timestamp into the mintId payload.
 *
 * Note: this contract requires a patch on OpenZeppelin ERC721 implementation such that the _update function does not emit
 * the Transfer event because it only has access to a mintId while Transfer events should be emitted using publicId.
 *
 * @notice This contract is marked as abstract because inheriting contract MUST override _getPayload function so that it returns
 * a unique payload every time the value of the NFT changes.
 */
abstract contract ERC721DynamicIds is ERC721Enumerable {
    /**
     * An event emited during the NFT minting that allows for efficient querying of an arbitrary data attached to the NFT.
     * @param mintId an immutable id with last 16 bytes zeroed. The same that is used internally.
     * @param data arbitrary data assigned to the NFT. This can include data that is either immutable or does not cause public id to change when updated.
     */
    event DynamicIdNFTMinted(uint256 indexed mintId, address indexed owner, bytes data);

    /**
     * This is a special event that allows for easy tracking of the NFT across value changes.
     * @param mintId an immutable id with last 16 bytes zeroed. The same that is used internally.
     * @param newPublicId new public id after the update happened.
     * @param payload payload resulting in the new public id.
     */
    event DynamicIdNFTUpdated(uint256 indexed mintId, uint256 indexed newPublicId, bytes payload);

    error InvalidMintId(uint256 mintId);

    /**
     * This mint function SHOULD be used instead of _safeMint as it takes care of emitting the right events.
     * @param to an address receiving an NFT
     * @param mintId an immutable id with last 16 bytes zeroed. The same that is used internally.
     * @param data arbitrary data assigned to the NFT. This can include data that is either immutable or does not cause public id to change when updated.
     */
    function _mintDynamicIdNFT(address to, uint256 mintId, bytes memory data) internal returns (uint256) {
        bytes memory payload = _getPayload(mintId);
        uint256 publicId = DynamicIds.createPublicId(mintId, payload);
        _safeMint(to, publicId, data);
        emit DynamicIdNFTMinted(mintId, to, data);
        emit DynamicIdNFTUpdated(mintId, publicId, payload);
        return publicId;
    }

    /**
     * This MUST be used every time a publicId is consumed as a parameter to:
     * - validate given publicId is valid
     * - get mintId to perform access storage correctly
     *
     * It is similar to _requireOwned from OpenZeppelin’s ERC721 implementation.
     *
     * @param publicId a public id of an NFT
     */
    function _requireValidPublicId(uint256 publicId) internal view returns (uint256 mintId) {
        mintId = DynamicIds.zeroLast16Bytes(publicId);
        bytes16 publicIdLast16Bytes = DynamicIds.getLast16Bytes(publicId);
        if (DynamicIds.getFirst16Bytes(keccak256(_getPayload(mintId))) != publicIdLast16Bytes) {
            revert ERC721NonexistentToken(publicId);
        }
    }

    /**
     * This MUST be used once in every transaction that changes the value of an NFT such that
     * the payload returned by the _getPayload function is diffrent than before.
     *
     * This lets the outside world know the old NFT has been burned and the new NFT has been minted.
     *
     * @param prevPublicId a public id of an NFT
     * @param mintId a mint id of an NFT
     */
    function _updatePublicId(uint256 prevPublicId, uint256 mintId) internal returns (uint256 newId) {
        bytes memory payload = _getPayload(mintId);
        newId = _getPublicId(mintId, payload);
        address owner = _ownerOf(mintId);

        _checkOnERC721Received(address(0), owner, newId, "");

        emit IERC721.Transfer(owner, address(0), prevPublicId);
        emit IERC721.Transfer(address(0), owner, newId);
        emit DynamicIdNFTUpdated(mintId, newId, payload);
    }

    /**
     * This function MUST be overriten by an inheriting smart contract such that the payload
     * changes every time the value of an underlying NFT changes.
     * @param mintId a mint id of an NFT
     */
    function _getPayload(uint256 mintId) internal view virtual returns (bytes memory payload);

    /**
     * Translates mintId to publicId. Useful for overriding functions that return mintId.
     * @param mintId a mint id of an NFT
     * @param payload the result of the _getPayload function
     */
    function _getPublicId(uint256 mintId, bytes memory payload) private pure returns (uint256 publicId) {
        return DynamicIds.createPublicId(mintId, payload);
    }

    /*
     * ERC721 overrides.
     *
     * We override all public methods that take tokenId as a parameter
     * except safeTransferFrom(address from, address to, uint256 tokenId)
     * because it calls
     * function safeTransferFrom(address from, address to, uint256 tokenId, bytes memory data)
     * so translating tokenId twice would be wasteful.
     *
     * All internal transactions must receive mintId to work correctly.
     *
     * We also override _safeMint to validate mintId and emit a proper Transfer event.
     */

    function ownerOf(uint256 publicId) public view override(ERC721, IERC721) returns (address) {
        uint256 mintId = _requireValidPublicId(publicId);
        return super.ownerOf(mintId);
    }

    function tokenURI(uint256 publicId) public view virtual override returns (string memory) {
        uint256 mintId = _requireValidPublicId(publicId);
        return super.tokenURI(mintId);
    }

    function approve(address to, uint256 publicId) public override(ERC721, IERC721) {
        uint256 mintId = _requireValidPublicId(publicId);
        super.approve(to, mintId);
        emit IERC721.Approval(_msgSender(), to, publicId);
    }

    function getApproved(uint256 publicId) public view override(ERC721, IERC721) returns (address) {
        uint256 mintId = _requireValidPublicId(publicId);
        return super.getApproved(mintId);
    }

    function transferFrom(address from, address to, uint256 publicId) public virtual override(ERC721, IERC721) {
        uint256 mintId = _requireValidPublicId(publicId);
        super.transferFrom(from, to, mintId);
        emit IERC721.Transfer(from, to, publicId);
    }

    function safeTransferFrom(address from, address to, uint256 publicId, bytes memory data)
        public
        override(ERC721, IERC721)
    {
        transferFrom(from, to, publicId);
        _checkOnERC721Received(from, to, publicId, data);
    }

    function _safeMint(address to, uint256 publicId, bytes memory data) internal override {
        if (DynamicIds.getLast16Bytes(publicId) == 0) revert InvalidMintId(publicId);
        uint256 mintId = _requireValidPublicId(publicId);
        super._safeMint(to, mintId, data);
        emit IERC721.Transfer(address(0), to, publicId);
    }

    /*
     * ERC721Enumerable overrides.
     *
     * We override all public methods that take tokenId as a parameter or return tokenId.
     *
     * All internal transactions must receive mintId to work correctly.
     */

    function tokenOfOwnerByIndex(address owner, uint256 index) public view override returns (uint256) {
        uint256 mintId = super.tokenOfOwnerByIndex(owner, index);

        return _getPublicId(mintId, _getPayload(mintId));
    }

    function tokenByIndex(uint256 index) public view override returns (uint256) {
        uint256 mintId = super.tokenByIndex(index);

        return _getPublicId(mintId, _getPayload(mintId));
    }
}
