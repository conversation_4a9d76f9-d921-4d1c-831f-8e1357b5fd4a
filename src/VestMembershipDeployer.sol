// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Deployer3 } from "src/utils/Deployer3.sol";

import { Membership } from "src/types/Configuration.sol";
import { VestMembership } from "src/VestMembership.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { IVestPresaleScheduler } from "src/IVestPresaleScheduler.sol";
import { IVestFeeCollectorProvider } from "./IVestFeeCollectorProvider.sol";

interface IVestMembershipDeployer {
    /// @notice Deploys new membership.
    function deploy(
        address owner,
        IVestPresaleScheduler scheduler,
        IVestFeeCollectorProvider feeCollectorProvider,
        Membership.Configuration memory configuration
    ) external returns (IVestMembership);

    /// @notice Returns the deterministic address of smart contract that will be deployed.
    function predict() external returns (address);
}

contract VestMembershipDeployer is Deployer3, IVestMembershipDeployer {
    /// @inheritdoc IVestMembershipDeployer
    function deploy(
        address owner,
        IVestPresaleScheduler scheduler,
        IVestFeeCollectorProvider feeCollectorProvider,
        Membership.Configuration memory configuration
    ) external returns (IVestMembership) {
        address deployed = deploy3(
            type(VestMembership).creationCode, abi.encode(owner, scheduler, feeCollectorProvider, configuration)
        );

        return IVestMembership(deployed);
    }

    /// @inheritdoc IVestMembershipDeployer
    function predict() external view returns (address) {
        return getDeployed();
    }
}
