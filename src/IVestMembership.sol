// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC721 } from "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import { IERC2981 } from "@openzeppelin/contracts/interfaces/IERC2981.sol";
import { IERC721Enumerable } from "@openzeppelin/contracts/token/ERC721/extensions/IERC721Enumerable.sol";

/**
 * @title IVestMembership
 * <AUTHOR> @notice
 */
interface IVestMembership is IERC2981, IERC721, IERC721Enumerable {
    struct Usage {
        uint256 max;
        uint256 current;
    }

    struct Metadata {
        address token;
        string color;
        string description;
    }

    struct Attributes {
        uint256 price;
        uint256 allocation;
        uint256 claimbackPeriod;
        uint32 tgeNumerator;
        uint32 tgeDenominator;
        uint32 cliffDuration;
        uint32 cliffNumerator;
        uint32 cliffDenominator;
        uint32 vestingPeriodCount;
        uint32 vestingPeriodDuration;
        uint8 tradeable;
    }

    /// @notice Creates new membership and transfers it to given owner.
    /// @param owner_ Address of new address owner.
    /// @param roundId Id of the assigned round.
    /// @param maxUsage Max usage of the new membership.
    /// @param attributes Attributes attached to the membership.
    function mint(address owner_, uint256 roundId, uint256 currentUsage, uint256 maxUsage, Attributes memory attributes)
        external
        returns (uint256);

    /// @notice Extends the membership maximum usage.
    /// @param publicId Id of the membership.
    /// @param amount The amount by which the maximum usage is to be increased.
    function extend(uint256 publicId, uint256 amount) external returns (uint256 newId);

    /// @notice Reduces the membership maximum usage.
    /// @param publicId Id of the membership.
    /// @param amount The amount by which the maximum usage is to be reduced.
    function reduce(uint256 publicId, uint256 amount) external returns (uint256 newId);

    /// @notice Increases the membership current usage.
    /// @param publicId Id of the membership.
    /// @param amount The amount by which the current usage is to be increased.
    function consume(uint256 publicId, uint256 amount) external returns (uint256 newId);

    /// @notice Returns the start timestamp.
    function getStartTimestamp() external view returns (uint256);

    /// @notice Returns the usage by given membership id.
    /// @param publicId Id of the membership.
    function getUsage(uint256 publicId) external view returns (Usage memory);

    /// @notice Returns the round by given membership id.
    /// @param publicId Id of the membership.
    function getRoundId(uint256 publicId) external view returns (uint256);

    /// @notice Returns the attributes by given membership id.
    /// @param publicId Id of the membership.
    function getAttributes(uint256 publicId) external view returns (Attributes memory);

    /// @notice Returns releasable amount in the given timestamp.
    /// @param publicId Id of the membership.
    function unlocked(uint256 publicId) external view returns (uint256);
    function unlocked(uint256 start, uint256 allocation, Attributes memory attributes)
        external
        view
        returns (uint256);
}
