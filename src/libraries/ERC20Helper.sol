// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { SafeERC20 } from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import { IERC20Metadata } from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";

/// @title ERC20Helper
/// @notice Contains helper methods for interacting with ERC20 tokens.
library ERC20Helper {
    /// @notice Transfers tokens from the calling contract to a recipient.
    /// @param token The contract address of the token which will be transferred.
    /// @param to The recipient of the transfer.
    /// @param value The value of the transfer.
    function transfer(IERC20 token, address to, uint256 value) internal {
        SafeERC20.safeTransfer(token, to, value);
    }

    /**
     * @notice Transfers tokens from sender to a recipient and returns transferred amount.
     * @param token The contract address of the token which will be transferred.
     * @param sender The sender of the transfer.
     * @param to The recipient of the transfer.
     * @param value The value of the transfer.
     *
     * @dev Transferring tokens in some protocol functions cannot rely on given `amount`
     * because in the case of a token that collects tax or handles the `transfer` in a
     * custom way. In that case the value may not reflect the actual transferred value.
     *
     * Solution:
     * - before the transfer: save the current balance
     * - after the transfer: subtract this value from the new balance
     */
    function transferFrom(IERC20 token, address sender, address to, uint256 value) internal returns (uint256) {
        uint256 balance = token.balanceOf(to);

        SafeERC20.safeTransferFrom(token, sender, to, value);

        return token.balanceOf(to) - balance;
    }

    /// @notice Returns the decimals places of the token.
    /// @param token The contract address of the token.
    function decimals(IERC20 token) internal view returns (uint256) {
        return IERC20Metadata(address(token)).decimals();
    }
}
