// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

library Errors {
    /// @notice Given value is out of safe bounds.
    error UnacceptableValue();

    /// @notice Given reference is `address(0)`.
    error UnacceptableReference();

    /// @notice The caller account is not authorized to perform an operation.
    /// @param account Address of the account.
    error Unauthorized(address account);

    /// @notice The caller account is not authorized to perform an operation.
    /// @param account Address of the account.
    error AccountMismatch(address account);

    /// @notice Denominators cannot equal zero because division by zero is not allowed.
    error DenominatorZero();
}
