// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Deployer3 } from "src/utils/Deployer3.sol";

import { Round } from "src/types/Round.sol";
import { Presale } from "src/types/Configuration.sol";
import { VestPresale } from "src/VestPresale.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { IVestFeeCollectorProvider } from "./IVestFeeCollectorProvider.sol";
import { IVestMembership } from "src/IVestMembership.sol";

interface IVestPresaleDeployer {
    /// @notice Deploys new presale.
    function deploy(
        IVestMembership membership,
        IVestFeeCollectorProvider feeCollectorProvider,
        Presale.Configuration memory configuration,
        Round[] memory rounds
    ) external returns (address);

    /// @notice Returns the deterministic address of smart contract that will be deployed.
    function predict() external returns (address);
}

contract VestPresaleDeployer is Deployer3, IVestPresaleDeployer {
    /// @inheritdoc IVestPresaleDeployer
    function deploy(
        IVestMembership membership,
        IVestFeeCollectorProvider feeCollectorProvider,
        Presale.Configuration memory configuration,
        Round[] memory rounds
    ) external returns (address) {
        return
            deploy3(type(VestPresale).creationCode, abi.encode(membership, feeCollectorProvider, configuration, rounds));
    }

    /// @inheritdoc IVestPresaleDeployer
    function predict() external view returns (address) {
        return getDeployed();
    }
}
