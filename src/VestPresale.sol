// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Context } from "@openzeppelin/contracts/utils/Context.sol";
import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";
import { EnumerableSet } from "@openzeppelin/contracts/utils/structs/EnumerableSet.sol";
import { MerkleProof } from "@openzeppelin/contracts/utils/cryptography/MerkleProof.sol";

import { Withdrawable } from "delegatecall/Withdrawable.sol";

import { Errors } from "src/libraries/Errors.sol";
import { MathHelper } from "src/libraries/MathHelper.sol";
import { ERC20Helper } from "src/libraries/ERC20Helper.sol";
import { Round, RoundState } from "src/types/Round.sol";
import { Presale } from "src/types/Configuration.sol";
import { IVestMembership } from "src/IVestMembership.sol";
import { IVestPresaleScheduler } from "src/IVestPresaleScheduler.sol";
import { IVestFeeCollectorProvider } from "src/IVestFeeCollectorProvider.sol";

/**
 * @title VestPresale
 * @notice An implementation of smart contract to handle the process of presale and vesting.
 */
contract VestPresale is Context, Withdrawable, IVestPresaleScheduler {
    using EnumerableSet for EnumerableSet.UintSet;

    uint256 internal constant ROUND_LOCK_PERIOD = 1 hours;

    /// @notice ERC20 implementation of the token sold.
    IERC20 public immutable tokenA;

    /// @notice ERC20 implementation of the token collected.
    IERC20 public immutable tokenB;

    /// @notice An address of a external membership smart contract.
    IVestMembership public immutable membership;

    /// @notice An address of a external smart contract that provide the fee collector address.
    IVestFeeCollectorProvider public immutable feeCollectorProvider;

    /// @notice Address of the manager.
    address public manager;

    /// @notice Address of the beneficiary.
    address public beneficiary;

    /// @notice Amount of tokens available to distribution during the vesting.
    uint256 public liquidityA;

    /// @notice Amount of tokens collected during the sale.
    uint256 public liquidityB;

    /// @notice Amount of tokens collected during the sale that are available to withdraw.
    uint256 public nonClaimableBackTokenB;

    /// @notice Timestamp indicating when the tge should be available.
    uint256 internal tgeTimestamp;

    /// @notice Timestamp indicating starting point from which the claim back period begins.
    uint256 internal listingTimestamp;

    /// @notice How much time in seconds since `listingTimestamp` do Users have to claimback TokenA
    uint256 public immutable claimbackPeriod;

    /// @notice Indicates whether the account participated in the sale state of given round.
    mapping(uint256 roundId => mapping(bytes32 => bool)) public roundParticipants;

    /// @notice Incremental value for indexing rounds.
    uint256 internal roundSerialId;

    /// @notice Fees applicable to this presale
    Presale.Fees internal fees;

    /// @notice List of rounds ids.
    EnumerableSet.UintSet internal roundsIds;

    /// @notice Collection of the rounds.
    mapping(uint256 roundId => Round) internal rounds;

    /// @notice Event emitted when the funds has been claimed.
    /// @param vMembershipId Id of the membership.
    /// @param amountA Amount of the claimed funds.
    event Claimed(uint256 indexed vMembershipId, uint256 amountA);

    /// @notice Event emitted when the funds has been claimed back.
    /// @param vMembershipId Id of the membership.
    /// @param amountA Amount of the claimed back funds.
    event ClaimedBack(uint256 indexed vMembershipId, uint256 amountA);

    /// @notice Event emitted when the funds has been deposited.
    /// @param amount Amount of the deposited funds.
    event DepositedA(uint256 amount);

    /// @notice Event emitted when the funds has been withdrawn.
    /// @param amount Amount of the withdrawn funds.
    event WithdrawnA(uint256 amount);

    /// @notice Event emitted when the funds has been withdrawn.
    /// @param amount Amount of the withdrawn funds.
    event WithdrawnB(uint256 amount);

    /// @notice Event emitted when the round has been updated.
    event RoundUpdated(uint256 indexed id);

    /// @notice Event emitted when the manager has been updated.
    event ManagerUpdated(address current);

    /// @notice Event emitted when the beneficiary has been updated.
    event BeneficiaryUpdated(address current);

    /// @notice Event emitted when the tge start timestamp has been updated.
    /// @param timestamp The new timestamp.
    event ListingTimestampUpdated(uint256 timestamp);

    /// @notice Event emitted when the tge listing difference timestamp has been updated.
    /// @param value The new value.
    event TgeTimestampUpdated(uint256 value);

    //-------------------------------------------------------------------------
    // Errors

    /// @notice Cannot update the locked round.
    /// @param id Id of the round.
    error RoundIsLocked(uint256 id);

    /// @notice The round with given id does not exist.
    /// @param id Id of the round.
    error RoundNotExists(uint256 id);

    /// @notice Round is in a different state.
    /// @param id The id of updated round.
    /// @param current Current state of the round.
    /// @param expected Expected state of the round.
    error RoundStateMismatch(uint256 id, RoundState current, RoundState expected);

    /// @notice Claim not allowed by given membership.
    /// @param membershipId Id of the membership.
    error ClaimNotAllowed(uint256 membershipId);

    /// @notice Claimback not allowed for given membership.
    /// @param membershipId Id of the membership.
    error ClaimbackNotAllowed(uint256 membershipId);

    /// @notice Cliffs that unblock tokens immediately are not allowed
    error CliffWithImmediateUnlock();

    /// @notice Vesting periods with duration 0 are not allowed
    error VestingWithImmediateUnlock();

    /// @notice Vesting with only one period is too short. Either use cliff or increase period count.
    error CliffLikeVesting();

    /// @notice The vesting is configured such that it would never unlock any tokens.
    error VestingWithoutUnlocks();

    /// @notice Cliff height is specified but no vesting periods follow. In that case, all tokens will be unlocked at cliff end so cliffNumerator should equal zero.
    error CliffHeightWithoutSubsequentUnlocks();

    /// @notice When vesting is configured such that it will never release 100% tokens or it will release more than 100% of tokens.
    error VestingSize();

    /// @notice This protocol does not support tokens with transfer fees
    error TokenWithTransferFees(address tokenAddress);

    /// @notice `LiquidityA` is lower than needed.
    error OutOfLiquidityA();

    /// @notice Given `account` is already a participant in the round.
    error AlreadyRoundParticipant(uint256 roundId, address account);

    /// @notice Ensures that the account is eligible for withdrawal.
    modifier protectedWithdrawal() override {
        if (_msgSender() != manager) revert Errors.Unauthorized(_msgSender());
        _;
    }

    /// @notice Ensure the sender is the manager.
    /// @param account Address of the sender.
    modifier onlyManager(address account) {
        if (account != manager) revert Errors.Unauthorized(account);
        _;
    }

    /// @notice Ensure the sender is the beneficiary.
    /// @param account Address of the sender.
    modifier onlyBeneficiary(address account) {
        if (account != beneficiary) revert Errors.Unauthorized(account);
        _;
    }

    /// @notice Ensure the sender is the owner of the membership.
    /// @param membershipId Id of the membership.
    modifier onlyMember(uint256 membershipId) {
        if (membership.ownerOf(membershipId) != _msgSender()) revert Errors.AccountMismatch(_msgSender());
        _;
    }

    /// @notice Ensures that the selected round has given state.
    /// @param roundId Id of the round.
    /// @param expected Expected state of the round.
    modifier onlyRoundInState(uint256 roundId, RoundState expected) {
        RoundState current = getRoundState(roundId);

        if (current != expected) revert RoundStateMismatch(roundId, current, expected);
        _;
    }

    /// @notice Contract state initialization.
    constructor(
        IVestMembership membership_,
        IVestFeeCollectorProvider feeCollectorProvider_,
        Presale.Configuration memory configuration,
        Round[] memory rounds_
    ) {
        if (address(membership_) == address(0)) revert Errors.UnacceptableReference();
        if (address(feeCollectorProvider_) == address(0)) revert Errors.UnacceptableReference();

        if (address(configuration.tokenA) == address(0)) revert Errors.UnacceptableReference();
        if (address(configuration.tokenB) == address(0)) revert Errors.UnacceptableReference();

        if (configuration.manager == address(0)) revert Errors.UnacceptableReference();
        if (configuration.beneficiary == address(0)) revert Errors.UnacceptableReference();

        if (configuration.listingTimestamp != 0 && configuration.tgeTimestamp == 0) {
            revert Errors.UnacceptableValue();
        }
        if (configuration.listingTimestamp != 0 && configuration.tgeTimestamp > configuration.listingTimestamp) {
            revert Errors.UnacceptableValue();
        }

        // not emitting an event since fees can’t change after being set in a constructor
        fees = configuration.fees;

        tokenB = configuration.tokenB;
        tokenA = configuration.tokenA;

        manager = configuration.manager;
        beneficiary = configuration.beneficiary;

        membership = membership_;
        feeCollectorProvider = feeCollectorProvider_;

        tgeTimestamp = configuration.tgeTimestamp;
        claimbackPeriod = configuration.claimbackPeriod;
        listingTimestamp = configuration.listingTimestamp;

        uint256 size = rounds_.length;
        for (uint256 i = 0; i < size; i++) {
            _addRound(rounds_[i]);
        }
    }

    //--------------------------------------------------------------------------
    // Domain

    function claim(uint256 membershipId) external onlyMember(membershipId) returns (uint256) {
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        // when is the first claim
        if (usage.current == 0) {
            uint256 timestamp = block.timestamp;

            // must be after tgeTimestamp
            if (tgeTimestamp == 0 || timestamp < tgeTimestamp) revert ClaimNotAllowed(membershipId);

            IVestMembership.Attributes memory attributes = membership.getAttributes(membershipId);

            if (attributes.price > 0) {
                uint256 denominator = 10 ** ERC20Helper.decimals(tokenA);

                // overflow not possible because `nonClaimableBackTokenB` always less than tokenB total supply.
                unchecked {
                    nonClaimableBackTokenB += usage.max * attributes.price / denominator;
                }
            }
        }

        uint256 unlocked = membership.unlocked(tgeTimestamp, usage.max, membership.getAttributes(membershipId));
        uint256 releasable = unlocked - usage.current;

        if (releasable == 0) revert ClaimNotAllowed(membershipId);

        uint256 newId = membership.consume(membershipId, releasable);

        ERC20Helper.transfer(tokenA, _msgSender(), releasable);

        emit Claimed(membershipId, releasable);

        return newId;
    }

    /// @notice Transfers `tokenB` tokens and creates the membership.
    /// @param roundId Id of the round.
    /// @param amountA amount of token A to buy
    /// @param attributes The membership attributes.
    /// @param proof Merkle tree proof.
    function buy(
        uint256 roundId,
        uint256 amountA,
        IVestMembership.Attributes calldata attributes,
        bytes32[] calldata proof
    ) external onlyRoundInState(roundId, RoundState.SALE) returns (uint256) {
        if (amountA == 0) revert Errors.UnacceptableValue();
        if (amountA > attributes.allocation) revert Errors.UnacceptableValue();

        _requireValidAttributes(attributes);
        _requireCallerCanParticipateInSale(roundId, attributes, proof);

        uint256 boughtA = _buy(0, attributes.allocation, attributes.price, amountA);
        return membership.mint(_msgSender(), roundId, 0, boughtA, attributes);
    }

    /// @notice Transfers `tokenB` tokens and updates usage of the given membership.
    /// @param membershipId Id of the membership.
    /// @param amountA Amount of tokens to extend the membership.
    function extend(uint256 membershipId, uint256 amountA)
        external
        onlyRoundInState(membership.getRoundId(membershipId), RoundState.SALE)
        onlyMember(membershipId)
        returns (uint256)
    {
        IVestMembership.Usage memory usage = membership.getUsage(membershipId);
        IVestMembership.Attributes memory attributes = membership.getAttributes(membershipId);

        uint256 released = _buy(usage.max, attributes.allocation, attributes.price, amountA);

        if (attributes.price > 0 && usage.current > 0) {
            uint256 denominator = 10 ** ERC20Helper.decimals(tokenA);

            // overflow not possible because `nonClaimableBackTokenB` always less than tokenB total supply.
            unchecked {
                nonClaimableBackTokenB += amountA * attributes.price / denominator;
            }
        }

        return membership.extend(membershipId, released);
    }

    /**
     * @notice Claimback `tokenB` tokens to the membership owner and adds `tokenA` tokens back to the `liquidityA`.
     * @param membershipId Id of the membership.
     * @param amountA Amount of `tokenA` tokens to claimback.
     *
     * Requirements:
     * - the caller must have a membership
     * - the membership `claimbackPeriod` attribute must be greater than zero
     * - when the `listingTimestamp` is different from zero => the current timestamp
     *   must be earlier than the sum of `listingTimestamp` and `period`
     */
    function claimback(uint256 membershipId, uint256 amountA)
        external
        onlyMember(membershipId)
        returns (uint256 newPublicId)
    {
        if (amountA == 0) revert ClaimbackNotAllowed(membershipId);

        IVestMembership.Attributes memory attributes = membership.getAttributes(membershipId);

        if (attributes.claimbackPeriod == 0) revert ClaimbackNotAllowed(membershipId);

        /**
         * The `period` cannot be greater than the `Presale.claimbackPeriod`, as this
         * is related to the `withdrawTokenB` function. The `withdrawTokenB` method allows
         * all `tokenB` funds to be withdrawn after the time for which the
         * `Presale.claimbackPeriod` is used to calculate.
         */
        uint256 period = MathHelper.min(claimbackPeriod, attributes.claimbackPeriod);
        if (listingTimestamp != 0 && block.timestamp >= listingTimestamp + period) {
            revert ClaimbackNotAllowed(membershipId);
        }

        IVestMembership.Usage memory usage = membership.getUsage(membershipId);

        if (usage.current > 0) revert ClaimbackNotAllowed(membershipId);

        uint256 claimableBackA = MathHelper.min(amountA, usage.max);

        // Calculates the number of `tokenB` tokens to be claimed back to the membership owner.
        uint256 denominatorA = 10 ** ERC20Helper.decimals(tokenA);
        uint256 claimableBackB = claimableBackA * attributes.price / denominatorA;

        if (claimableBackB == 0) revert ClaimbackNotAllowed(membershipId);

        unchecked {
            liquidityA += claimableBackA;
            liquidityB -= claimableBackB;
        }

        newPublicId = membership.reduce(membershipId, claimableBackA);
        ERC20Helper.transfer(tokenB, _msgSender(), claimableBackB);

        emit ClaimedBack(membershipId, claimableBackA);
    }

    /// @notice Updates the manager address.
    /// @param value Address of the new manager.
    function updateManager(address value) external onlyManager(_msgSender()) {
        if (value == address(0)) revert Errors.UnacceptableReference();

        manager = value;

        emit ManagerUpdated(value);
    }

    /// @notice Updates the beneficiary address.
    /// @param value Address of the new beneficiary.
    function updateBeneficiary(address value) external onlyBeneficiary(_msgSender()) {
        if (value == address(0)) revert Errors.UnacceptableReference();

        beneficiary = value;

        emit BeneficiaryUpdated(value);
    }

    /// @notice Updates tge timestamp value.
    /// @param timestamp new tge timestamp.
    function updateTgeTimestamp(uint256 timestamp) external onlyBeneficiary(_msgSender()) {
        // The value cannot be in the past.
        if (timestamp < block.timestamp) revert Errors.UnacceptableValue();

        // Cannot set tge timestamp to be greater than listing timestamp.
        if (listingTimestamp != 0 && timestamp > listingTimestamp) revert Errors.UnacceptableValue();

        tgeTimestamp = timestamp;

        emit TgeTimestampUpdated(timestamp);
    }

    /// @notice Updates listing timestamp value.
    /// @param timestamp new listing timestamp.
    function updateListingTimestamp(uint256 timestamp) external onlyBeneficiary(_msgSender()) {
        // The value cannot be in the past.
        if (timestamp < block.timestamp) revert Errors.UnacceptableValue();

        // Cannot set listing timestamp to be less than tge timestamp.
        if (tgeTimestamp == 0 || timestamp < tgeTimestamp) revert Errors.UnacceptableValue();

        listingTimestamp = timestamp;

        emit ListingTimestampUpdated(timestamp);
    }

    /// @notice Deposits the `tokenA`.
    /// @param amountA Amount of the tokens to deposit.
    function depositTokenA(uint256 amountA) external {
        uint256 deposited = ERC20Helper.transferFrom(tokenA, _msgSender(), address(this), amountA);

        if (deposited != amountA) revert TokenWithTransferFees(address(tokenA));

        // overflow is not possible because transferred amount cannot be higher than token supply.
        unchecked {
            liquidityA += deposited;
        }

        emit DepositedA(deposited);
    }

    /// @inheritdoc Withdrawable
    function withdrawToken(address to, IERC20 token, uint256 amount) public override protectedWithdrawal {
        if (address(token) == address(tokenA) || address(token) == address(tokenB)) {
            revert Errors.UnacceptableReference();
        }

        super.withdrawToken(to, token, amount);
    }

    /// @notice Beneficiary can withdraw `tokenA` at any time.
    /// @param amount amount of `tokenA` to withdraw.
    function withdrawTokenA(uint256 amount) external onlyBeneficiary(_msgSender()) {
        if (amount > liquidityA) revert Errors.UnacceptableValue();

        // underflow not possible because we checked before that `amount` < `liquidityA`
        unchecked {
            liquidityA -= amount;
        }

        ERC20Helper.transfer(tokenA, beneficiary, amount);

        emit WithdrawnA(amount);
    }

    /// @notice Withdraws the `tokenB` tokens to the beneficiary.
    function withdrawTokenB() external {
        if (listingTimestamp == 0) revert Errors.UnacceptableValue();

        uint256 withdrawable = nonClaimableBackTokenB;

        // if claimback timestamp in past beneficiary can get all liquidity.
        if (block.timestamp > listingTimestamp + claimbackPeriod) {
            withdrawable = liquidityB;
        }

        if (withdrawable == 0) revert Errors.UnacceptableValue();

        nonClaimableBackTokenB = 0;
        unchecked {
            liquidityB -= withdrawable;
        }

        uint256 fee;
        Presale.Fees memory fees_ = fees;
        if (fees_.tokenBNumerator != 0 && fees_.tokenBDenominator != 0) {
            // over/underflow not possible here because fee always will be less than `withdrawable` such as percent of fee cannot be higher or equal than 100.
            unchecked {
                fee = (withdrawable * fees_.tokenBNumerator) / fees_.tokenBDenominator;
            }

            if (fee > 0) {
                unchecked {
                    withdrawable -= fee;
                }

                ERC20Helper.transfer(tokenB, getFeeCollector(), fee);
            }
        }

        ERC20Helper.transfer(tokenB, beneficiary, withdrawable);

        emit WithdrawnB(withdrawable + fee);
    }

    //--------------------------------------------------------------------------
    // Rounds configuration

    /// @notice Adds new round.
    /// @param round Configuration of the round.
    function addRound(Round memory round) external onlyManager(_msgSender()) {
        _addRound(round);
    }

    /// @notice Updates the round.
    /// @param roundId Id of the round.
    /// @param round Configuration of the round.
    function updateRound(uint256 roundId, Round memory round) external onlyManager(_msgSender()) {
        if (round.startTimestamp >= round.endTimestamp) revert Errors.UnacceptableValue();

        if (block.timestamp >= rounds[roundId].startTimestamp - ROUND_LOCK_PERIOD) {
            revert RoundIsLocked(roundId);
        }

        rounds[roundId] = round;

        emit RoundUpdated(roundId);
    }

    /// @notice Removes the round.
    /// @param roundId Id of the round.
    function removeRound(uint256 roundId) external onlyManager(_msgSender()) {
        if (!roundsIds.contains(roundId)) revert RoundNotExists(roundId);

        if (block.timestamp >= rounds[roundId].startTimestamp - ROUND_LOCK_PERIOD) {
            revert RoundIsLocked(roundId);
        }

        roundsIds.remove(roundId);

        emit RoundUpdated(roundId);
    }

    /// @notice Updates the round whitelist configuration.
    /// @param roundId Id of the round.
    /// @param whitelistRoot Merkle tree root.
    /// @param proofsUri The uri of the proofs.
    function updateWhitelist(uint256 roundId, bytes32 whitelistRoot, string memory proofsUri)
        external
        onlyManager(_msgSender())
    {
        if (!roundsIds.contains(roundId)) revert RoundNotExists(roundId);

        rounds[roundId].proofsUri = proofsUri;
        rounds[roundId].whitelistRoot = whitelistRoot;

        emit RoundUpdated(roundId);
    }

    //--------------------------------------------------------------------------
    // Misc

    /// @notice Returns the fees configuration.
    function getFees() external view returns (Presale.Fees memory) {
        return fees;
    }

    /// @notice Returns the list of the rounds and their ids and states.
    function getRounds()
        external
        view
        returns (uint256[] memory ids, Round[] memory rounds_, RoundState[] memory states)
    {
        ids = roundsIds.values();

        uint256 size = ids.length;
        rounds_ = new Round[](size);
        states = new RoundState[](size);
        for (uint256 i = 0; i < size; i++) {
            rounds_[i] = rounds[ids[i]];
            states[i] = getRoundState(ids[i]);
        }

        return (ids, rounds_, states);
    }

    /// @notice Returns the round.
    /// @param roundId Id of the round.
    function getRound(uint256 roundId) public view returns (Round memory) {
        if (!roundsIds.contains(roundId)) revert RoundNotExists(roundId);

        return rounds[roundId];
    }

    /// @notice Returns the round state.
    /// @param roundId Id of the round.
    function getRoundState(uint256 roundId) public view returns (RoundState) {
        if (!roundsIds.contains(roundId)) revert RoundNotExists(roundId);

        uint256 timestamp = block.timestamp;

        if (timestamp < rounds[roundId].startTimestamp) return RoundState.PENDING;

        if (timestamp >= rounds[roundId].endTimestamp || liquidityA == 0) {
            return RoundState.VESTING;
        }

        return RoundState.SALE;
    }

    /// @inheritdoc IVestPresaleScheduler
    function getTgeTimestamp() public view returns (uint256) {
        return tgeTimestamp;
    }

    /// @inheritdoc IVestPresaleScheduler
    function getListingTimestamp() public view returns (uint256) {
        return listingTimestamp;
    }

    /// @notice Returns the fee collector address.
    function getFeeCollector() public view returns (address) {
        return feeCollectorProvider.getFeeCollector();
    }

    /// @notice Adds new round.
    /// @param round Configuration of the round.
    function _addRound(Round memory round) internal {
        if (bytes(round.name).length == 0) revert Errors.UnacceptableValue();
        if (round.startTimestamp == 0 || round.endTimestamp == 0) revert Errors.UnacceptableValue();
        if (round.startTimestamp >= round.endTimestamp) revert Errors.UnacceptableValue();

        unchecked {
            ++roundSerialId;
        }

        roundsIds.add(roundSerialId);

        rounds[roundSerialId] = round;

        emit RoundUpdated(roundSerialId);
    }

    /**
     * @notice Pays for `tokenA` with `tokenB`
     * @param bought how much a participant already bought
     * @param allocation what’s participant’s allocation
     * @param price attributes.price of the membership
     * @param amountA Amount of tokenA to extend the membership.
     */
    // slither-disable-next-line reentrancy-no-eth
    function _buy(uint256 bought, uint256 allocation, uint256 price, uint256 amountA) internal returns (uint256) {
        uint256 available = MathHelper.min(liquidityA, allocation - bought);
        uint256 buyingA = MathHelper.min(amountA, available);

        if (buyingA == 0) revert OutOfLiquidityA();

        uint256 tokenADecimals = ERC20Helper.decimals(tokenA);
        uint256 amountBPaidSoFar = bought * price / 10 ** tokenADecimals;
        uint256 amountBSumAfterThisTransaction = (bought + buyingA) * price / 10 ** tokenADecimals;
        uint256 amountB = amountBSumAfterThisTransaction - amountBPaidSoFar;
        if (amountB > 0) {
            uint256 receivedB = ERC20Helper.transferFrom(tokenB, _msgSender(), address(this), amountB);

            if (receivedB != amountB) revert TokenWithTransferFees(address(tokenB));

            unchecked {
                liquidityB += amountB;
            }
        }

        unchecked {
            liquidityA -= buyingA;
        }

        return buyingA;
    }

    /**
     * @notice Ensure the given attributes meet the requirements.
     *
     * @param attributes The membership attributes.
     */
    function _requireValidAttributes(IVestMembership.Attributes calldata attributes) internal pure {
        if (attributes.tgeDenominator == 0 || attributes.cliffDenominator == 0) revert Errors.DenominatorZero();

        if (attributes.cliffNumerator > 0 && attributes.cliffDuration == 0) revert CliffWithImmediateUnlock();

        if (attributes.vestingPeriodCount > 0 && attributes.vestingPeriodDuration == 0) {
            revert VestingWithImmediateUnlock();
        }

        if (attributes.vestingPeriodCount == 1) revert CliffLikeVesting();

        if (attributes.tgeNumerator == 0 && attributes.cliffDuration == 0 && attributes.vestingPeriodCount == 0) {
            revert VestingWithoutUnlocks();
        }

        if (attributes.vestingPeriodCount == 0 && attributes.cliffNumerator > 0) {
            revert CliffHeightWithoutSubsequentUnlocks();
        }

        if (
            attributes.cliffDuration == 0 && attributes.vestingPeriodCount == 0
                && attributes.tgeNumerator != attributes.tgeDenominator
        ) revert VestingSize();

        if (attributes.tgeNumerator > attributes.tgeDenominator) revert VestingSize();

        if (attributes.cliffNumerator > attributes.cliffDenominator) revert VestingSize();

        if (attributes.tgeNumerator > 0 && attributes.cliffNumerator > 0) {
            uint256 commonDenominator = attributes.tgeDenominator * attributes.cliffDenominator;
            uint256 totalUnlocks = attributes.tgeNumerator * attributes.cliffDenominator
                + attributes.cliffNumerator * attributes.tgeDenominator;
            if (totalUnlocks > commonDenominator) revert VestingSize();
        }
    }

    /**
     * @notice Ensure the sender can participate in sale state of a given round.
     *
     * Account cannot participate in the sale state if it is not on the whitelist or has
     * already participated in a round.
     *
     * @param roundId Id of the round.
     * @param attributes The membership attributes.
     * @param proof Merkle tree proof.
     */
    function _requireCallerCanParticipateInSale(
        uint256 roundId,
        IVestMembership.Attributes calldata attributes,
        bytes32[] calldata proof
    ) internal {
        address account = _msgSender();

        bytes32 key = keccak256(
            abi.encode(
                account,
                attributes.price,
                attributes.allocation,
                attributes.claimbackPeriod,
                attributes.tgeNumerator,
                attributes.tgeDenominator,
                attributes.cliffDuration,
                attributes.cliffNumerator,
                attributes.cliffDenominator,
                attributes.vestingPeriodCount,
                attributes.vestingPeriodDuration,
                attributes.tradeable
            )
        );

        if (roundParticipants[roundId][key]) revert AlreadyRoundParticipant(roundId, account);

        if (!MerkleProof.verify(proof, rounds[roundId].whitelistRoot, key)) revert Errors.AccountMismatch(account);

        roundParticipants[roundId][key] = true;
    }
}
