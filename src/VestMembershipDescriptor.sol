// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { Base64 } from "@openzeppelin/contracts/utils/Base64.sol";
import { Strings } from "@openzeppelin/contracts/utils/Strings.sol";
import { IERC20Metadata } from "@openzeppelin/contracts/token/ERC20/extensions/IERC20Metadata.sol";

import { IVestMembership } from "src/IVestMembership.sol";
import { MembershipSVG } from "src/libraries/MembershipSVG.sol";

interface IVestMembershipDescriptor {
    /// @notice Generates the name of the membership.
    /// @param metadata Metadata of the membership.
    function name(IVestMembership.Metadata memory metadata) external view returns (string memory);

    /// @notice Generates the symbol of the membership.
    /// @param metadata Metadata of the membership.
    function symbol(IVestMembership.Metadata memory metadata) external view returns (string memory);

    /// @notice Generates encoded JSON metadata.
    /// @param start Date of the start.
    /// @param usage Usage of the membership.
    /// @param metadata Metadata of the membership.
    /// @param attributes Attributes of the membership.
    /// @return encoded JSON metadata in base64.
    function tokenURI(
        uint256 start,
        IVestMembership.Usage memory usage,
        IVestMembership.Metadata memory metadata,
        IVestMembership.Attributes memory attributes
    ) external view returns (string memory);
}

contract VestMembershipDescriptor is IVestMembershipDescriptor {
    using Strings for address;
    using Strings for uint32;
    using Strings for uint256;

    /// @inheritdoc IVestMembershipDescriptor
    function name(IVestMembership.Metadata memory metadata) public view returns (string memory) {
        string memory name_ = IERC20Metadata(address(metadata.token)).name();

        return string.concat(name_, " Vesting");
    }

    /// @inheritdoc IVestMembershipDescriptor
    function symbol(IVestMembership.Metadata memory metadata) public view returns (string memory) {
        string memory symbol_ = IERC20Metadata(address(metadata.token)).symbol();

        return string.concat("v", symbol_);
    }

    /// @inheritdoc IVestMembershipDescriptor
    function tokenURI(
        uint256 start,
        IVestMembership.Usage memory usage,
        IVestMembership.Metadata memory metadata,
        IVestMembership.Attributes memory attributes
    ) public view virtual returns (string memory) {
        string memory json = string.concat(
            '{"attributes":',
            _traits(start, usage, metadata, attributes),
            ',"description":"',
            metadata.description,
            '","name":"',
            _title(metadata),
            '","image":"',
            _image(usage, metadata),
            '"}'
        );

        return string.concat("data:application/json;base64,", Base64.encode(bytes(json)));
    }

    /// @notice Generates title for given membership.
    /// @param metadata Metadata of the membership.
    function _title(IVestMembership.Metadata memory metadata) internal view returns (string memory) {
        string memory symbol_ = IERC20Metadata(address(metadata.token)).symbol();

        return string.concat("Vesting of ", symbol_);
    }

    /// @notice Generates encoded image.
    /// @param usage Usage of the membership.
    /// @param metadata Metadata of the membership.
    /// @return encoded image.
    function _image(IVestMembership.Usage memory usage, IVestMembership.Metadata memory metadata)
        internal
        view
        returns (string memory)
    {
        uint256 denominator = 10 ** IERC20Metadata(address(metadata.token)).decimals();

        string memory svg = MembershipSVG.generate(
            MembershipSVG.Params({
                color: metadata.color,
                title: name(metadata),
                max: usage.max / denominator,
                current: usage.current / denominator
            })
        );

        return string.concat("data:image/svg+xml;base64,", Base64.encode(bytes(svg)));
    }

    /// @notice Generates traits metadata.
    /// @param start Date of the start.
    /// @param usage Usage of the membership.
    /// @param metadata Metadata of the membership.
    /// @return encoded image.
    function _traits(
        uint256 start,
        IVestMembership.Usage memory usage,
        IVestMembership.Metadata memory metadata,
        IVestMembership.Attributes memory attributes
    ) internal view returns (string memory) {
        uint256 denominator = 10 ** IERC20Metadata(address(metadata.token)).decimals();

        string memory traits0 = string.concat(
            '[{"trait_type":"Usage","display_type":"boost_percentage","value":',
            (usage.max > 0 ? usage.current * 100 / usage.max : 0).toString(),
            '},{"trait_type":"Vested tokens","display_type":"number","value":',
            Strings.toString(usage.max / denominator),
            '},{"trait_type":"Claimed tokens","display_type":"number","value":',
            Strings.toString(usage.current / denominator),
            '},{"trait_type":"TGE","display_type":"boost_percentage","value":',
            (attributes.tgeDenominator > 0 ? attributes.tgeNumerator * 100 / attributes.tgeDenominator : 0).toString(),
            '},{"trait_type":"Vesting start","display_type":"date","value":',
            start.toString(),
            '},{"trait_type":"Vesting end","display_type":"date","value":',
            (start + attributes.cliffDuration + (attributes.vestingPeriodCount * attributes.vestingPeriodDuration))
                .toString()
        );

        /// @dev split to avoid the stack too deep error
        string memory traits1 = string.concat(
            '},{"trait_type":"Cliff duration","value":"',
            _getCliffDurationText(attributes.cliffDuration),
            '"},{"trait_type":"Cliff unlock","display_type":"boost_percentage","value":',
            (attributes.cliffDenominator > 0 ? attributes.cliffNumerator * 100 / attributes.cliffDenominator : 0)
                .toString(),
            '},{"trait_type":"Unlock frequency","value":"',
            _getUnlockFrequencyText(attributes.vestingPeriodDuration),
            '"},{"trait_type":"Vested token name","value":"',
            IERC20Metadata(address(metadata.token)).name(),
            '"},{"trait_type":"Vested token symbol","value":"',
            IERC20Metadata(address(metadata.token)).symbol(),
            '"},{"trait_type":"Vested token address","value":"',
            Strings.toHexString(uint160(metadata.token), 20),
            '"}]'
        );

        return string.concat(traits0, traits1);
    }

    /// @notice Convert the cliff duration to human-readable value.
    /// @param value Value of the cliff duration.
    /// @return Human-readable value.
    function _getCliffDurationText(uint256 value) internal pure virtual returns (string memory) {
        if (value == 0) return "no cliff";

        (uint256 period, string memory label) = _humanize(value);

        return string.concat(period.toString(), " ", label);
    }

    /// @notice Convert the unlock frequency to human-readable value.
    /// @param value Value of the unlock frequency.
    /// @return Human-readable value.
    function _getUnlockFrequencyText(uint256 value) internal pure virtual returns (string memory) {
        if (value == 0) return "none";

        (uint256 period, string memory label) = _humanize(value);

        if (period == 1) return string.concat("every ", label);

        return string.concat("every ", period.toString(), " ", label);
    }

    /// @notice Convert the period to a human-readable value.
    /// @param value Period to humanize.
    /// @return Period in as text value.
    function _humanize(uint256 value) internal pure virtual returns (uint256, string memory) {
        if (value < 1 hours) return _pluralize(value / 1 minutes, "minute", "minutes");

        if (value < 1 days) return _pluralize(value / 1 hours, "hour", "hours");

        return _pluralize(value / 1 days, "day", "days");
    }

    /// @notice Returns a label based on the given value.
    /// @param value The value on which the selection of the label is based.
    /// @param singular Singular label.
    /// @param plural Plural label.
    /// @return Generated label.
    function _pluralize(uint256 value, string memory singular, string memory plural)
        internal
        pure
        virtual
        returns (uint256, string memory)
    {
        return (value, value == 1 ? singular : plural);
    }
}
