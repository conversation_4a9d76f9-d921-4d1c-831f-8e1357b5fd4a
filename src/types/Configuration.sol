// SPDX-License-Identifier: UNLICENSED
pragma solidity 0.8.23;

import { IERC20 } from "@openzeppelin/contracts/interfaces/IERC20.sol";

import { IVestMembership } from "src/IVestMembership.sol";
import { IVestMembershipDescriptor } from "src/VestMembershipDescriptor.sol";

/// @notice Namespace for the structs related with the presale configuration.
library Presale {
    struct Fees {
        uint16 tokenANumerator;
        uint16 tokenADenominator;
        uint16 tokenBNumerator;
        uint16 tokenBDenominator;
    }

    struct Configuration {
        Fees fees;
        IERC20 tokenA;
        IERC20 tokenB;
        address manager;
        address beneficiary;
        uint256 tgeTimestamp;
        uint256 listingTimestamp;
        uint256 claimbackPeriod;
    }
}

/// @notice Namespace for the structs related with the membership configuration.
library Membership {
    struct Fees {
        uint16 numerator;
        uint16 denominator;
    }

    struct Configuration {
        Fees fees;
        IVestMembership.Metadata metadata;
        IVestMembershipDescriptor descriptor;
    }
}
